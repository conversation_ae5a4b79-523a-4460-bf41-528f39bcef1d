# 多模态Graph2ECPE扩展

## 📋 项目概述

本项目成功将原有的Graph2ECPE（基于图到序列的情感原因对提取）模型扩展为支持多模态输入的版本，借鉴了MMGCN的多模态对话图建模策略。扩展后的模型能够同时处理文本、视觉和音频三种模态的信息，用于对话场景下的情感原因对提取任务。

## 🚀 主要特性

### ✨ 多模态支持
- **文本模态**: 使用预训练语言模型（RoBERTa）编码话语文本
- **视觉模态**: 处理4096维的视觉特征（来自视频帧）
- **音频模态**: 处理6373维的音频特征（来自语音信号）

### 🔗 图建模策略
- **模态内连接**: 基于余弦相似度构建同模态话语间的连接
- **模态间连接**: 连接同一话语的不同模态表示
- **说话者嵌入**: 为每个模态添加独立的说话者嵌入
- **模态嵌入**: 为不同模态添加可学习的模态标识

### 🎯 序列生成
- 保持原有的Graph2Seq架构用于情感原因对的序列生成
- 支持束搜索和贪婪解码两种生成策略
- 输出格式：`<sos> utt_001 emotion1 utt_002 <sep> utt_003 emotion3 utt_004 <sep> ... <eos>`

## 📁 文件结构

```
graph2ecpe/
├── data/
│   ├── multimodal_dataset.py          # 多模态数据集加载器
│   └── ...
├── models/
│   ├── multimodal_fusion.py           # 多模态特征融合模块
│   ├── multimodal_graph_encoder.py    # 多模态图编码器
│   ├── multimodal_graph2seq_ecpe.py   # 多模态主模型
│   └── ...
├── train_multimodal.py                # 多模态训练脚本
├── test_multimodal.py                 # 多模态测试脚本
└── MULTIMODAL_README.md              # 本文档
```

## 🛠️ 核心组件

### 1. MultimodalDialogDataset
- 支持HDF5格式的多模态数据集加载
- 兼容MELD和IEMOCAP数据集格式
- 自动处理特征归一化和数据预处理

### 2. MultimodalFeatureFusion
- 实现文本、视觉、音频特征的投影和融合
- 支持说话者嵌入和模态嵌入
- 借鉴MMGCN的特征处理策略

### 3. MultimodalGraphEncoder
- 扩展原有图编码器以支持多模态输入
- 支持GNN和GAT两种图卷积方式
- 实现多模态图构建和编码

### 4. MultimodalGraph2SeqECPE
- 主模型类，整合所有多模态组件
- 保持与原有单模态模型的兼容性
- 支持灵活的模态组合配置

## 📊 数据格式

### HDF5数据结构
```
conversation_1/
├── utterance_IDs           # [1, 2, 3, ...]
├── texts                   # ["Hello", "How are you?", ...]
├── speakers                # ["Speaker1", "Speaker2", ...]
├── emotions                # ["neutral", "joy", ...]
├── visual_features         # (n_utterances, 4096)
├── audio_features          # (n_utterances, 6373)
└── emotion_cause_pairs     # ["3_joy|1", "5_anger|4", ...]
```

### 批次数据格式
```python
batch = (
    dialog_tokens,                      # [batch_size, seq_len]
    dialog_uttid,                       # [batch_size, seq_len]
    dialog_mask,                        # [batch_size, seq_len]
    utt_mask,                          # [batch_size, max_utts]
    utt_speakers,                      # [batch_size, max_utts]
    utt_emotions,                      # [batch_size, max_utts]
    edge_index_batch,                  # List[List[List[int]]]
    edge_type_batch,                   # List[List[int]]
    edge_norm_batch,                   # List[List[float]]
    emotion_cause_edge_indices_batch,  # List[List[int]]
    visual_features_batch,             # List[Tensor]
    audio_features_batch               # List[Tensor]
)
```

## 🔧 配置参数

### 多模态相关配置
```python
# 多模态配置
use_multimodal: bool = True
modalities: List[str] = ['text', 'visual', 'audio']
visual_dim: int = 4096
audio_dim: int = 6373
use_speaker_embedding: bool = True
use_modal_embedding: bool = True
use_multimodal_graph: bool = True
normalize_multimodal_features: bool = True
```

## 🚀 使用方法

### 1. 训练多模态模型
```bash
python train_multimodal.py \
    --dataset meld \
    --batch_size 8 \
    --learning_rate 2e-5 \
    --epochs 20 \
    --use_multimodal True
```

### 2. 测试功能
```bash
python test_multimodal.py
```

### 3. 代码示例
```python
from config import create_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models import MultimodalGraph2SeqECPE

# 创建配置
config = create_config(dataset_name="meld")
config.model.use_multimodal = True
config.model.modalities = ['text', 'visual', 'audio']

# 创建数据集
dataset = MultimodalDialogDataset(
    h5_file_path="data/meld/meld_train_multimodal.h5",
    modalities=['text', 'visual', 'audio']
)

# 创建模型
model = MultimodalGraph2SeqECPE(config.model)

# 训练或推理
outputs, attention = model(batch)
```

## 📈 性能特点

### 模型规模
- **参数数量**: ~356M（包含多模态组件）
- **内存占用**: 根据批次大小和序列长度动态调整
- **计算复杂度**: O(n²) 图注意力 + O(n) 序列生成

### 支持的模态组合
- 单模态：`['text']`
- 双模态：`['text', 'visual']`, `['text', 'audio']`, `['visual', 'audio']`
- 三模态：`['text', 'visual', 'audio']`

## 🔍 测试结果

所有核心功能测试通过：
- ✅ 多模态数据集加载（984个对话）
- ✅ 多模态模型前向传播
- ✅ 多模态训练兼容性
- ✅ 预测和生成功能

## 🎯 技术亮点

### 1. 借鉴MMGCN策略
- 采用MMGCN的多模态图构建方法
- 实现模态内和模态间的有效连接
- 支持说话者和模态嵌入

### 2. 保持架构兼容性
- 完全兼容原有的Graph2ECPE架构
- 支持单模态和多模态的无缝切换
- 保持原有的序列生成能力

### 3. 灵活的配置系统
- 支持多种模态组合
- 可配置的图构建策略
- 灵活的特征处理选项

## 🔮 未来扩展

### 可能的改进方向
1. **注意力机制优化**: 实现跨模态注意力机制
2. **图结构增强**: 添加更多类型的边连接
3. **预训练策略**: 设计多模态预训练任务
4. **效率优化**: 实现更高效的多模态融合
5. **评估指标**: 开发多模态特定的评估指标

### 数据集扩展
- 支持更多多模态对话数据集
- 添加更多模态（如手势、表情等）
- 支持不同分辨率的视觉特征

## 📝 注意事项

1. **数据要求**: 需要HDF5格式的多模态数据集
2. **内存需求**: 多模态特征会增加内存占用
3. **计算资源**: 建议使用GPU进行训练和推理
4. **特征质量**: 视觉和音频特征的质量直接影响模型性能

## 🤝 贡献

本扩展成功整合了：
- Graph2ECPE的序列生成架构
- MMGCN的多模态图建模策略
- 现代深度学习的最佳实践

通过这个扩展，原有的单模态情感原因对提取系统现在能够充分利用多模态信息，为对话理解和情感分析提供更强大的能力。
