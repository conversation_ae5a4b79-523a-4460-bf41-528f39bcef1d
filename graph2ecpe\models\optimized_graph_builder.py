"""
优化的图构建模块
解决图构建计算复杂度过高的问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Optional


class OptimizedGraphBuilder:
    """
    优化的图构建器，解决O(n²)计算复杂度问题
    """
    
    def __init__(self, 
                 connection_strategy: str = 'efficient_cosine',
                 similarity_threshold: float = 0.1,
                 max_connections: int = 10,
                 use_sparse: bool = True):
        """
        Args:
            connection_strategy: 连接策略 ('efficient_cosine', 'top_k', 'threshold')
            similarity_threshold: 相似度阈值
            max_connections: 每个节点的最大连接数
            use_sparse: 是否使用稀疏矩阵
        """
        self.connection_strategy = connection_strategy
        self.similarity_threshold = similarity_threshold
        self.max_connections = max_connections
        self.use_sparse = use_sparse
    
    def create_multimodal_adjacency_efficient(self,
                                            features_list: List[torch.Tensor],
                                            utterance_lengths: List[int],
                                            modalities: List[str] = ['text', 'visual', 'audio']) -> torch.Tensor:
        """
        高效创建多模态邻接矩阵
        
        Args:
            features_list: 特征列表，每个元素对应一个模态的特征
            utterance_lengths: 每个对话的话语数量
            modalities: 模态列表
            
        Returns:
            adj_matrix: 优化的多模态邻接矩阵
        """
        device = features_list[0].device
        n_modals = len(features_list)
        total_length = sum(utterance_lengths)
        
        # 创建稀疏邻接矩阵的索引和值
        edge_indices = []
        edge_weights = []
        
        # 1. 高效构建模态内连接
        self._build_intra_modal_connections_efficient(
            edge_indices, edge_weights, features_list, utterance_lengths, n_modals, total_length
        )
        
        # 2. 高效构建模态间连接
        self._build_inter_modal_connections_efficient(
            edge_indices, edge_weights, features_list, utterance_lengths, n_modals, total_length
        )
        
        # 3. 构建稀疏邻接矩阵
        if len(edge_indices) > 0:
            # 转换edge_indices为tensor格式
            edge_indices_tensor = torch.tensor(edge_indices, device=device, dtype=torch.long).t()
            edge_weights_tensor = torch.tensor(edge_weights, device=device, dtype=torch.float32)

            if self.use_sparse:
                # 创建稀疏矩阵
                adj_matrix = torch.sparse_coo_tensor(
                    edge_indices_tensor,
                    edge_weights_tensor,
                    (n_modals * total_length, n_modals * total_length),
                    device=device
                ).coalesce()

                # 转换为密集矩阵（如果需要）
                adj_matrix = adj_matrix.to_dense()
            else:
                # 直接构建密集矩阵
                adj_matrix = torch.zeros((n_modals * total_length, n_modals * total_length), device=device)
                for idx, (i, j) in enumerate(edge_indices):
                    adj_matrix[i, j] = edge_weights[idx]
        else:
            # 空邻接矩阵
            adj_matrix = torch.zeros((n_modals * total_length, n_modals * total_length), device=device)
        
        return adj_matrix
    
    def _build_intra_modal_connections_efficient(self,
                                               edge_indices: List,
                                               edge_weights: List,
                                               features_list: List[torch.Tensor],
                                               utterance_lengths: List[int],
                                               n_modals: int,
                                               total_length: int):
        """高效构建模态内连接"""
        start_idx = 0
        
        for dia_len in utterance_lengths:
            for modal_idx, features in enumerate(features_list):
                modal_feat = features[start_idx:start_idx + dia_len]
                
                if self.connection_strategy == 'efficient_cosine':
                    # 批量计算余弦相似度矩阵
                    sim_matrix = self._compute_cosine_similarity_batch(modal_feat)
                    
                    # 应用阈值过滤
                    mask = sim_matrix > self.similarity_threshold
                    
                    # 获取有效连接
                    valid_i, valid_j = torch.where(mask)
                    
                    for idx in range(len(valid_i)):
                        i, j = valid_i[idx].item(), valid_j[idx].item()
                        if i != j:  # 排除自连接
                            global_i = start_idx + i + modal_idx * total_length
                            global_j = start_idx + j + modal_idx * total_length
                            weight = sim_matrix[i, j].item()

                            edge_indices.append([global_i, global_j])
                            edge_weights.append(weight)
                
                elif self.connection_strategy == 'top_k':
                    # Top-K连接策略
                    sim_matrix = self._compute_cosine_similarity_batch(modal_feat)
                    
                    for i in range(dia_len):
                        # 获取每个节点的top-k邻居
                        similarities = sim_matrix[i]
                        similarities[i] = -1  # 排除自连接
                        
                        top_k = min(self.max_connections, dia_len - 1)
                        _, top_indices = torch.topk(similarities, top_k)
                        
                        for j in top_indices:
                            j = j.item()
                            global_i = start_idx + i + modal_idx * total_length
                            global_j = start_idx + j + modal_idx * total_length
                            weight = similarities[j].item()

                            edge_indices.append([global_i, global_j])
                            edge_weights.append(weight)
            
            start_idx += dia_len
    
    def _build_inter_modal_connections_efficient(self,
                                               edge_indices: List,
                                               edge_weights: List,
                                               features_list: List[torch.Tensor],
                                               utterance_lengths: List[int],
                                               n_modals: int,
                                               total_length: int):
        """高效构建模态间连接"""
        start_idx = 0
        
        for dia_len in utterance_lengths:
            # 对角线连接：同一话语的不同模态
            for i in range(dia_len):
                for m1 in range(n_modals):
                    for m2 in range(m1 + 1, n_modals):
                        # 计算模态间相似度
                        feat_m1 = features_list[m1][start_idx + i]
                        feat_m2 = features_list[m2][start_idx + i]
                        
                        # 使用高效的相似度计算
                        similarity = F.cosine_similarity(
                            feat_m1.unsqueeze(0), 
                            feat_m2.unsqueeze(0)
                        ).item()
                        
                        if similarity > self.similarity_threshold:
                            global_i = start_idx + i + m1 * total_length
                            global_j = start_idx + i + m2 * total_length

                            # 双向连接
                            edge_indices.append([global_i, global_j])
                            edge_weights.append(similarity)
                            edge_indices.append([global_j, global_i])
                            edge_weights.append(similarity)
            
            start_idx += dia_len
    
    def _compute_cosine_similarity_batch(self, features: torch.Tensor) -> torch.Tensor:
        """
        批量计算余弦相似度矩阵
        
        Args:
            features: [n, d] 特征矩阵
            
        Returns:
            similarity_matrix: [n, n] 相似度矩阵
        """
        # 归一化特征
        norm_features = F.normalize(features, p=2, dim=1)
        
        # 批量计算相似度矩阵
        similarity_matrix = torch.mm(norm_features, norm_features.t())
        
        # 处理数值稳定性
        similarity_matrix = torch.clamp(similarity_matrix, -0.99999, 0.99999)
        
        # 转换为权重（可选）
        if hasattr(self, 'use_acos_transform') and self.use_acos_transform:
            similarity_matrix = 1 - torch.acos(similarity_matrix) / np.pi
        
        return similarity_matrix
    
    def create_edge_index_from_adjacency(self, adj_matrix: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        从邻接矩阵创建边索引和边权重
        
        Args:
            adj_matrix: 邻接矩阵
            
        Returns:
            edge_index: [2, num_edges] 边索引
            edge_weights: [num_edges] 边权重
        """
        if adj_matrix.is_sparse:
            # 处理稀疏矩阵
            indices = adj_matrix.indices()
            values = adj_matrix.values()
            
            # 过滤零权重边
            mask = values > self.similarity_threshold
            edge_index = indices[:, mask]
            edge_weights = values[mask]
        else:
            # 处理密集矩阵
            mask = adj_matrix > self.similarity_threshold
            edge_index = torch.nonzero(mask, as_tuple=False).t()
            edge_weights = adj_matrix[mask]
        
        return edge_index, edge_weights


class AdaptiveGraphBuilder(OptimizedGraphBuilder):
    """
    自适应图构建器，根据对话长度自动选择最优策略
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.length_thresholds = {
            'small': 10,   # 小对话：< 10 话语
            'medium': 50,  # 中对话：10-50 话语
            'large': 100   # 大对话：> 50 话语
        }
    
    def create_multimodal_adjacency_adaptive(self,
                                           features_list: List[torch.Tensor],
                                           utterance_lengths: List[int],
                                           modalities: List[str] = ['text', 'visual', 'audio']) -> torch.Tensor:
        """
        自适应创建多模态邻接矩阵
        """
        max_length = max(utterance_lengths)
        
        # 根据对话长度选择策略
        if max_length < self.length_thresholds['small']:
            # 小对话：使用完全连接
            self.connection_strategy = 'efficient_cosine'
            self.similarity_threshold = 0.0
        elif max_length < self.length_thresholds['medium']:
            # 中对话：使用阈值过滤
            self.connection_strategy = 'efficient_cosine'
            self.similarity_threshold = 0.1
        else:
            # 大对话：使用Top-K连接
            self.connection_strategy = 'top_k'
            self.max_connections = min(10, max_length // 5)
        
        return self.create_multimodal_adjacency_efficient(
            features_list, utterance_lengths, modalities
        )
