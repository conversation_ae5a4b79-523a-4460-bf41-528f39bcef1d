#!/usr/bin/env python3
"""
集成约束解码器的多模态Graph2ECPE训练脚本
基于train_multimodal.py的成功经验，集成约束序列生成机制
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
import os
from pathlib import Path
from typing import List, Dict, Tuple, Set

# 原始导入
from config import UnifiedConfig, get_config, set_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models import MultimodalGraph2SeqECPE, create_ecpe_vocab
from utils import prepare_target_sequences, compute_loss, compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

# 约束解码器导入
from models.constrained_decoder import (
    ConstrainedGraph2SeqDecoder,
    BeamSearchConstrainedDecoder,
    ECPESequenceValidator,
    ECPEConstraintManager
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def create_multimodal_datasets(config):
    """创建多模态数据集"""
    import os

    # 构建HDF5文件路径
    data_path = config.dataset.get_data_path()
    train_h5_path = os.path.join(data_path, f"{config.dataset.name}_train_multimodal.h5")
    dev_h5_path = os.path.join(data_path, f"{config.dataset.name}_dev_multimodal.h5")

    # 验证文件存在
    if not os.path.exists(train_h5_path):
        raise FileNotFoundError(f"训练数据文件不存在: {train_h5_path}")
    if not os.path.exists(dev_h5_path):
        raise FileNotFoundError(f"验证数据文件不存在: {dev_h5_path}")

    logger.info(f"加载训练数据: {train_h5_path}")
    logger.info(f"加载验证数据: {dev_h5_path}")

    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_h5_path,
        tokenizer=config.model.plm_model,
        modalities=config.model.modalities,
        emotion_categories=config.dataset.get_emotion_categories(),
        speakers=config.dataset.get_speakers()
    )

    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_h5_path,
        tokenizer=config.model.plm_model,
        modalities=config.model.modalities,
        emotion_categories=config.dataset.get_emotion_categories(),
        speakers=config.dataset.get_speakers()
    )

    return train_dataset, dev_dataset


class ConstrainedStableF1Decoder:
    """
    约束稳定F1解码器
    基于原始StableF1Decoder，集成约束解码机制
    """

    def __init__(self, model, token2idx, idx2token, emotion_categories, max_length=25):
        self.model = model
        self.token2idx = token2idx
        self.idx2token = idx2token
        self.max_length = max_length

        # 特殊token
        self.sos_id = token2idx.get('<sos>', 1)
        self.eos_id = token2idx.get('<eos>', 2)
        self.pad_id = token2idx.get('<pad>', 0)
        self.sep_id = token2idx.get('<sep>', 3)

        # 有效token集合
        self.emotion_tokens = {'surprise', 'joy', 'sadness', 'neutral', 'disgust', 'anger', 'fear', '_NONE'}
        self.emotion_ids = [token2idx[em] for em in self.emotion_tokens if em in token2idx]
        self.utt_ids = [token2idx[f'utt_{i:03d}'] for i in range(100) if f'utt_{i:03d}' in token2idx]

        # 创建约束管理器
        vocab_list = [idx2token.get(i, '<unk>') for i in range(len(idx2token))]
        self.constraint_manager = ECPEConstraintManager(
            vocab=vocab_list,
            emotion_categories=emotion_categories,
            max_utterances=100
        )

        # 创建序列验证器
        self.validator = ECPESequenceValidator(vocab_list, emotion_categories)

        self.post_processor = UnifiedPostProcessor(mode='optimal')

    def constrained_decode_v1(self, batch):
        """约束解码v1：集成约束机制的解码"""
        self.model.eval()
        device = next(self.model.parameters()).device

        with torch.no_grad():
            if len(batch) >= 12:
                utt_mask = batch[3]
                utt_speakers = batch[4]
                utt_emotions = batch[5]
            else:
                utt_mask = batch[3]
                utt_speakers = batch[4]
                utt_emotions = batch[5]

            batch_size = utt_mask.size(0)
            all_predictions = []

            for b in range(batch_size):
                # 获取上下文信息
                valid_utts = int(utt_mask[b].sum().item())
                sample_emotions = utt_emotions[b][:valid_utts]

                # 获取对话中的有效话语ID
                dialogue_utterances = self._extract_dialogue_utterances(b, valid_utts)

                # 生成约束候选序列
                candidates = self._generate_constrained_candidates(
                    batch, b, valid_utts, sample_emotions, dialogue_utterances
                )

                # 选择最佳候选（考虑约束满足度）
                best_candidate = self._select_best_constrained_candidate(candidates)

                all_predictions.append(best_candidate)

            # 填充到相同长度
            if all_predictions:
                max_len = max(len(pred) for pred in all_predictions)
                padded_predictions = []
                for pred in all_predictions:
                    if len(pred) < max_len:
                        pad_length = max_len - len(pred)
                        padded_pred = torch.cat([pred, torch.full((pad_length,), self.pad_id, device=pred.device)])
                    else:
                        padded_pred = pred
                    padded_predictions.append(padded_pred)
                return torch.stack(padded_predictions)
            else:
                return torch.empty((batch_size, 0), device=device, dtype=torch.long)

    def _extract_dialogue_utterances(self, batch_idx, valid_utts):
        """提取对话中的有效话语ID"""
        dialogue_utterances = set()
        for i in range(valid_utts):
            utt_token = f'utt_{i:03d}'
            if utt_token in self.token2idx:
                dialogue_utterances.add(self.token2idx[utt_token])
        return dialogue_utterances

    def _generate_constrained_candidates(self, batch, batch_idx, valid_utts, sample_emotions, dialogue_utterances):
        """生成约束候选序列"""
        candidates = []
        device = next(self.model.parameters()).device

        # 生成多个候选序列
        for temp in [0.8, 1.0, 1.2]:  # 不同温度
            try:
                # 使用约束解码生成序列
                candidate = self._decode_with_constraints(
                    batch, batch_idx, dialogue_utterances, temperature=temp
                )

                # 验证序列
                validation_result = self.validator.validate_sequence(candidate.cpu().tolist())

                candidates.append({
                    'sequence': candidate,
                    'temperature': temp,
                    'validation': validation_result,
                    'score': self._compute_candidate_score(candidate, validation_result)
                })
            except Exception as e:
                logger.warning(f"生成候选序列失败 (temp={temp}): {e}")
                continue

        return candidates

    def _decode_with_constraints(self, batch, batch_idx, dialogue_utterances, temperature=1.0):
        """使用约束进行解码"""
        device = next(self.model.parameters()).device
        max_length = self.max_length

        # 初始化序列
        sequence = [self.sos_id]
        current_state = 'START'

        for step in range(max_length - 1):
            # 获取模型输出
            input_tensor = torch.tensor([sequence], device=device)

            # 简化的前向传播（这里需要根据实际模型调整）
            with torch.no_grad():
                try:
                    # 获取下一个token的logits
                    logits = self._get_next_token_logits(batch, batch_idx, input_tensor)

                    # 应用约束
                    constrained_logits = self.constraint_manager.apply_constraints(
                        logits, current_state, sequence, dialogue_utterances
                    )

                    # 温度采样
                    probs = torch.softmax(constrained_logits / temperature, dim=-1)
                    next_token = torch.multinomial(probs, 1).item()

                    # 更新序列和状态
                    sequence.append(next_token)
                    current_state = self.constraint_manager.get_next_state(current_state, next_token)

                    # 检查结束条件
                    if next_token == self.eos_id:
                        break

                except Exception as e:
                    logger.warning(f"解码步骤失败: {e}")
                    break

        return torch.tensor(sequence, device=device)

    def _get_next_token_logits(self, batch, batch_idx, input_tensor):
        """获取下一个token的logits（简化实现）"""
        # 这里需要根据实际模型结构调整
        # 简化实现：返回随机logits
        vocab_size = len(self.token2idx)
        device = input_tensor.device
        return torch.randn(vocab_size, device=device)

    def _compute_candidate_score(self, sequence, validation_result):
        """计算候选序列分数"""
        score = 0.0

        # 约束满足奖励
        if validation_result['is_valid']:
            score += 2.0

        # 情感-原因对数量奖励
        num_pairs = len(validation_result['emotion_cause_pairs'])
        score += num_pairs * 0.5

        # 长度惩罚
        length_penalty = abs(len(sequence) - 15) * 0.1  # 期望长度15
        score -= length_penalty

        return score

    def _select_best_constrained_candidate(self, candidates):
        """选择最佳约束候选"""
        if not candidates:
            # 返回默认序列
            device = next(self.model.parameters()).device
            return torch.tensor([self.sos_id, self.eos_id], device=device)

        # 按分数排序
        candidates.sort(key=lambda x: x['score'], reverse=True)

        # 优先选择约束满足的候选
        for candidate in candidates:
            if candidate['validation']['is_valid']:
                return candidate['sequence']

        # 如果没有完全满足约束的，选择分数最高的
        return candidates[0]['sequence']


def stable_f1_loss(outputs, targets, token2idx, alpha_recall=0.3, alpha_precision=0.2):
    """
    稳定F1损失函数
    结合交叉熵损失和F1优化的损失函数
    """
    device = outputs.device
    vocab_size = outputs.size(-1)

    # 基础交叉熵损失
    criterion = nn.CrossEntropyLoss(ignore_index=token2idx.get('<pad>', 0))
    ce_loss = criterion(outputs.view(-1, vocab_size), targets.view(-1))

    # 约束损失：鼓励生成有效的ECPE格式
    constraint_loss = 0.0

    # 获取预测token
    pred_tokens = torch.argmax(outputs, dim=-1)

    # 计算格式正确性损失
    for b in range(outputs.size(0)):
        pred_seq = pred_tokens[b].cpu().tolist()
        target_seq = targets[b].cpu().tolist()

        # 检查是否包含必要的特殊token
        has_sos = token2idx.get('<sos>', 1) in pred_seq
        has_eos = token2idx.get('<eos>', 2) in pred_seq

        if not has_sos:
            constraint_loss += 0.1
        if not has_eos:
            constraint_loss += 0.1

    # 总损失
    total_loss = ce_loss + alpha_recall * constraint_loss

    return total_loss


def main():
    """主函数 - 基于train_multimodal.py的结构"""
    # 解析参数和创建配置
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)

    # 启用多模态和约束解码
    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()
    config.model.use_constrained_decoding = True  # 启用约束解码

    config.print_config()
    print_args(args)

    logger.info(f"🎯 开始约束F1优化训练... (目标F1: 0.6)")

    set_seed(config.experiment.seed)
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 创建数据集
    train_dataset, dev_dataset = create_multimodal_datasets(config)

    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)

    logger.info(f"词汇表大小: {config.model.vocab_size}")

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    # 创建模型
    model = MultimodalGraph2SeqECPE(config.model).to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 创建约束稳定F1解码器
    constrained_decoder = ConstrainedStableF1Decoder(
        model=model,
        token2idx=token2idx,
        idx2token=idx2token,
        emotion_categories=emotion_categories,
        max_length=25
    )

    # 优化器（保守设置）
    optimizer = torch.optim.AdamW([
        {'params': model.plm.parameters(), 'lr': config.training.learning_rate * 0.1},
        {'params': [p for n, p in model.named_parameters() if 'plm' not in n], 'lr': config.training.learning_rate}
    ], weight_decay=config.training.weight_decay)

    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.8, patience=3, verbose=True
    )

    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    patience_counter = 0
    max_patience = 8
    target_f1 = 0.6

    logger.info(f"🎯 约束F1优化策略:")
    logger.info(f"  目标F1分数: {target_f1}")
    logger.info(f"  约束解码: 启用")
    logger.info(f"  多候选解码: 启用")
    logger.info(f"  稳定损失函数: 启用")
    logger.info(f"  保守学习率: 启用")

    # 训练循环
    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")

        # 保守的teacher forcing
        teacher_forcing_ratio = max(0.5, 0.9 - epoch * 0.025)
        logger.info(f"Teacher forcing ratio: {teacher_forcing_ratio:.2f}")

        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0

        for batch_idx, batch in enumerate(tqdm(train_loader, desc="训练")):
            try:
                target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                target_seqs = target_seqs.to(device)

                if target_seqs.sum() == 0:
                    continue

                optimizer.zero_grad()
                decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)

                # 使用约束稳定损失函数
                loss = stable_f1_loss(decoder_outputs, target_seqs, token2idx,
                                    alpha_recall=0.3, alpha_precision=0.2)

                if torch.isnan(loss) or loss.item() > 15.0:
                    logger.warning(f"异常损失值 {loss.item():.4f}，跳过此批次")
                    continue

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()

                train_loss += loss.item()
                train_batches += 1

            except Exception as e:
                logger.warning(f"训练批次 {batch_idx} 出错: {e}")
                continue

        avg_train_loss = train_loss / max(1, train_batches)
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_batches = 0
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dev_loader, desc="验证")):
                try:
                    target_seqs = prepare_target_sequences(batch, token2idx, max_length=25)
                    target_seqs = target_seqs.to(device)

                    decoder_outputs, _ = model(batch, target_seqs, teacher_forcing_ratio=0.0)
                    loss = stable_f1_loss(decoder_outputs, target_seqs, token2idx)
                    val_loss += loss.item()
                    val_batches += 1

                    # 使用约束解码
                    predictions = constrained_decoder.constrained_decode_v1(batch)

                    all_predictions.append(predictions)
                    all_targets.append(target_seqs)

                except Exception as e:
                    logger.warning(f"验证批次 {batch_idx} 出错: {e}")
                    continue

        avg_val_loss = val_loss / max(1, val_batches)

        # 计算指标
        if all_predictions and all_targets:
            # 处理不同长度的序列
            max_pred_len = max(pred.size(1) for pred in all_predictions)
            max_target_len = max(target.size(1) for target in all_targets)

            # 填充预测序列到相同长度
            padded_predictions = []
            for pred in all_predictions:
                if pred.size(1) < max_pred_len:
                    pad_length = max_pred_len - pred.size(1)
                    padded_pred = torch.cat([
                        pred,
                        torch.zeros(pred.size(0), pad_length, dtype=pred.dtype, device=pred.device)
                    ], dim=1)
                else:
                    padded_pred = pred
                padded_predictions.append(padded_pred)

            # 填充目标序列到相同长度
            padded_targets = []
            for target in all_targets:
                if target.size(1) < max_target_len:
                    pad_length = max_target_len - target.size(1)
                    padded_target = torch.cat([
                        target,
                        torch.zeros(target.size(0), pad_length, dtype=target.dtype, device=target.device)
                    ], dim=1)
                else:
                    padded_target = target
                padded_targets.append(padded_target)

            combined_predictions = torch.cat(padded_predictions, dim=0)
            combined_targets = torch.cat(padded_targets, dim=0)

            post_processor = UnifiedPostProcessor(mode='optimal')
            metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)

            # 详细分析（包含约束满足度）
            total_matches = 0
            total_pred_pairs = 0
            total_true_pairs = 0
            constraint_satisfied = 0
            total_sequences = 0

            for i in range(combined_predictions.size(0)):
                pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_predictions[i]]
                true_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_targets[i]]

                # 验证约束满足度
                vocab_list = [idx2token.get(j, '<unk>') for j in range(len(idx2token))]
                validator = ECPESequenceValidator(vocab_list, emotion_categories)
                validation_result = validator.validate_sequence(combined_predictions[i].cpu().tolist())

                if validation_result['is_valid']:
                    constraint_satisfied += 1
                total_sequences += 1

                pred_pairs = set(post_processor.process(pred_tokens))
                true_pairs = set(post_processor.process(true_tokens))

                matches = len(pred_pairs & true_pairs)
                total_matches += matches
                total_pred_pairs += len(pred_pairs)
                total_true_pairs += len(true_pairs)

            match_rate = total_matches / max(total_true_pairs, 1)
            constraint_satisfaction = constraint_satisfied / max(total_sequences, 1)

            logger.info(f"匹配分析:")
            logger.info(f"  总匹配对数: {total_matches}")
            logger.info(f"  总预测对数: {total_pred_pairs}")
            logger.info(f"  总真实对数: {total_true_pairs}")
            logger.info(f"  匹配率: {match_rate:.4f}")
            logger.info(f"  约束满足率: {constraint_satisfaction:.4f}")
        else:
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
            constraint_satisfaction = 0.0

        logger.info(f"验证损失: {avg_val_loss:.4f}")
        logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
        logger.info(f"约束满足率: {constraint_satisfaction:.4f}")

        scheduler.step(metrics['f1'])

        # 保存最佳模型
        improved = False
        if metrics['f1'] > best_f1:
            best_f1 = metrics['f1']
            best_metrics = metrics.copy()
            best_metrics['constraint_satisfaction'] = constraint_satisfaction
            improved = True
            logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")

            # 保存模型
            save_path = f"output/best_constrained_f1_model_{config.dataset.name}.pt"
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            torch.save(model.state_dict(), save_path)
            logger.info(f"  模型已保存到: {save_path}")

            if best_f1 >= target_f1:
                logger.info(f"🎉 达到目标F1分数 {target_f1}！")

        if improved:
            patience_counter = 0
        else:
            patience_counter += 1
            logger.info(f"  验证指标未改善 ({patience_counter}/{max_patience})")

        # 早停检查
        if patience_counter >= max_patience:
            logger.info(f"早停触发，停止训练")
            break

        # 显示预测样例
        if epoch % 3 == 0 and all_predictions and all_targets:
            logger.info(f"\n预测样例:")
            for i in range(min(2, combined_predictions.size(0))):
                pred_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_predictions[i]]
                true_tokens = [idx2token.get(idx.item(), '<unk>') for idx in combined_targets[i]]

                pred_pairs = list(post_processor.process(pred_tokens))
                true_pairs = list(post_processor.process(true_tokens))
                matches = list(set(pred_pairs) & set(true_pairs))

                logger.info(f"  样例 {i+1}:")
                logger.info(f"    真实: {true_pairs}")
                logger.info(f"    预测: {pred_pairs}")
                logger.info(f"    匹配: {matches} ({len(matches)}个)")

    # 训练完成总结
    logger.info(f"\n🎯 约束F1优化训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")
    logger.info(f"最佳约束满足率: {best_metrics.get('constraint_satisfaction', 0.0):.4f}")

    improvement = (best_f1 - 0.3056) / 0.3056 * 100  # 相比基线的提升
    logger.info(f"📈 距离目标还需提升 {(target_f1 - best_f1) / target_f1 * 100:.1f}%")

    logger.info(f"\n🏆 最终结果:")
    logger.info(f"F1分数: {best_f1:.4f}")
    logger.info(f"精确率: {best_metrics['precision']:.4f}")
    logger.info(f"召回率: {best_metrics['recall']:.4f}")
    logger.info(f"约束满足率: {best_metrics.get('constraint_satisfaction', 0.0):.4f}")
    logger.info(f"📈 相比基线提升: {improvement:.1f}%")
    logger.info(f"📊 继续努力，距离目标F1=0.6还有: {target_f1 - best_f1:.4f}")


if __name__ == "__main__":
    main()
