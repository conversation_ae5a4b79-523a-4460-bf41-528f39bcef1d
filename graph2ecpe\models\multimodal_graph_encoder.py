"""
多模态图编码器
借鉴MMGCN的多模态图卷积策略，扩展原有的图编码器以支持多模态输入
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Optional

from .graph_encoders import GNN, EnhancedGATEncoder
from .multimodal_fusion import MultimodalFeatureFusion, MultimodalGraphBuilder


class MultimodalGraphEncoder(nn.Module):
    """
    多模态图编码器
    结合MMGCN的多模态处理策略和Graph2ECPE的图编码架构
    """
    
    def __init__(self,
                 text_dim: int = 768,
                 visual_dim: int = 4096,
                 audio_dim: int = 6373,
                 hidden_dim: int = 256,
                 num_layers: int = 2,
                 num_heads: int = 8,
                 num_edge_types: int = 13,
                 n_speakers: int = 10,
                 modalities: List[str] = ['text', 'visual', 'audio'],
                 encoder_type: str = 'gat',  # 'gnn' or 'gat'
                 use_speaker_embedding: bool = True,
                 use_modal_embedding: bool = True,
                 use_multimodal_graph: bool = True,
                 dropout: float = 0.2,
                 pooling_method: str = 'attention'):
        """
        初始化多模态图编码器
        
        Args:
            text_dim: 文本特征维度
            visual_dim: 视觉特征维度
            audio_dim: 音频特征维度
            hidden_dim: 隐藏层维度
            num_layers: 图卷积层数
            num_heads: 注意力头数（GAT）
            num_edge_types: 边类型数量
            n_speakers: 说话者数量
            modalities: 使用的模态列表
            encoder_type: 编码器类型
            use_speaker_embedding: 是否使用说话者嵌入
            use_modal_embedding: 是否使用模态嵌入
            use_multimodal_graph: 是否使用多模态图结构
            dropout: dropout率
            pooling_method: 池化方法
        """
        super().__init__()
        
        self.modalities = modalities
        self.hidden_dim = hidden_dim
        self.encoder_type = encoder_type
        self.use_multimodal_graph = use_multimodal_graph
        
        # 多模态特征融合模块
        self.multimodal_fusion = MultimodalFeatureFusion(
            text_dim=text_dim,
            visual_dim=visual_dim,
            audio_dim=audio_dim,
            hidden_dim=hidden_dim,
            n_speakers=n_speakers,
            modalities=modalities,
            use_speaker_embedding=use_speaker_embedding,
            use_modal_embedding=use_modal_embedding,
            dropout=dropout
        )
        
        # 图编码器
        if encoder_type == 'gat':
            self.graph_encoder = EnhancedGATEncoder(
                input_size=hidden_dim,
                hidden_size=hidden_dim,
                num_layers=num_layers,
                num_heads=num_heads,
                num_edge_types=num_edge_types,
                dropout=dropout,
                pooling_method=pooling_method
            )
        else:
            self.graph_encoder = GNN(
                in_channels=hidden_dim,
                hidden_channels=hidden_dim,
                out_channels=hidden_dim,
                num_layers=num_layers,
                dropout=dropout,
                num_edge_types=num_edge_types,
                pooling_method=pooling_method
            )
        
        # 多模态图构建器
        if use_multimodal_graph:
            self.multimodal_graph_builder = MultimodalGraphBuilder()
    
    def forward(self,
                text_features: torch.Tensor,
                visual_features: List[torch.Tensor],
                audio_features: List[torch.Tensor],
                speaker_ids: torch.Tensor,
                utterance_lengths: torch.Tensor,
                edge_index: torch.Tensor,
                edge_type: torch.Tensor,
                edge_norm: torch.Tensor,
                batch_indices: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            text_features: 文本特征 [batch_size, max_utts, text_dim]
            visual_features: 视觉特征列表
            audio_features: 音频特征列表
            speaker_ids: 说话者ID [batch_size, max_utts]
            utterance_lengths: 话语数量 [batch_size]
            edge_index: 边索引 [2, num_edges]
            edge_type: 边类型 [num_edges]
            edge_norm: 边权重 [num_edges]
            batch_indices: batch索引 [total_nodes]
            
        Returns:
            node_embeddings: 节点嵌入 [total_nodes, hidden_dim]
            graph_embedding: 图嵌入 [batch_size, hidden_dim]
        """
        # 1. 多模态特征融合
        fused_features, fusion_batch_indices = self.multimodal_fusion(
            text_features, visual_features, audio_features, speaker_ids, utterance_lengths
        )
        
        # 2. 图编码
        if self.use_multimodal_graph and len(self.modalities) > 1:
            # 使用多模态图结构
            node_embeddings, graph_embedding = self._encode_multimodal_graph(
                fused_features, visual_features, audio_features, 
                utterance_lengths, fusion_batch_indices
            )
        else:
            # 使用原始图结构
            node_embeddings, graph_embedding = self.graph_encoder(
                fused_features, edge_index, edge_type, edge_norm, fusion_batch_indices
            )
        
        return node_embeddings, graph_embedding
    
    def _encode_multimodal_graph(self,
                                fused_features: torch.Tensor,
                                visual_features: List[torch.Tensor],
                                audio_features: List[torch.Tensor],
                                utterance_lengths: torch.Tensor,
                                batch_indices: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        使用多模态图结构进行编码
        """
        device = fused_features.device
        
        # 准备多模态特征列表
        features_list = []
        
        # 文本特征（已融合）
        features_list.append(fused_features)
        
        # 视觉特征
        if 'visual' in self.modalities and visual_features:
            visual_concat = torch.cat([vf for vf in visual_features if vf.size(0) > 0], dim=0)
            if visual_concat.size(0) == fused_features.size(0):
                # 投影到相同维度
                visual_projected = self.multimodal_fusion.visual_fc(visual_concat)
                features_list.append(visual_projected)
        
        # 音频特征
        if 'audio' in self.modalities and audio_features:
            audio_concat = torch.cat([af for af in audio_features if af.size(0) > 0], dim=0)
            if audio_concat.size(0) == fused_features.size(0):
                # 投影到相同维度
                audio_projected = self.multimodal_fusion.audio_fc(audio_concat)
                features_list.append(audio_projected)
        
        # 如果只有一个模态，使用原始图编码
        if len(features_list) == 1:
            # 创建简单的自连接图
            num_nodes = fused_features.size(0)
            edge_index = torch.arange(num_nodes, device=device).unsqueeze(0).repeat(2, 1)
            edge_type = torch.zeros(num_nodes, dtype=torch.long, device=device)
            edge_norm = torch.ones(num_nodes, device=device)
            
            return self.graph_encoder(fused_features, edge_index, edge_type, edge_norm, batch_indices)
        
        # 构建多模态邻接矩阵
        utterance_lengths_list = [ul.item() for ul in utterance_lengths]
        adj_matrix = self.multimodal_graph_builder.create_multimodal_adjacency(
            features_list, utterance_lengths_list, self.modalities
        )
        
        # 将邻接矩阵转换为边格式
        edge_index, edge_weight = self._adj_to_edge_index(adj_matrix)
        
        # 拼接所有模态特征
        all_features = torch.cat(features_list, dim=0)
        
        # 扩展batch索引
        extended_batch_indices = batch_indices.repeat(len(features_list))
        
        # 创建边类型（简化为单一类型）
        edge_type = torch.zeros(edge_index.size(1), dtype=torch.long, device=device)
        
        # 图编码
        node_embeddings, graph_embedding = self.graph_encoder(
            all_features, edge_index, edge_type, edge_weight, extended_batch_indices
        )
        
        # 提取对应的节点嵌入（取第一个模态的结果）
        num_original_nodes = fused_features.size(0)
        final_node_embeddings = node_embeddings[:num_original_nodes]
        
        return final_node_embeddings, graph_embedding
    
    def _adj_to_edge_index(self, adj_matrix: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        将邻接矩阵转换为边索引格式
        """
        # 找到非零元素
        edge_index = adj_matrix.nonzero().t().contiguous()
        edge_weight = adj_matrix[edge_index[0], edge_index[1]]
        
        return edge_index, edge_weight


class MultimodalGCNLayer(nn.Module):
    """
    多模态图卷积层
    借鉴MMGCN的GCNII结构
    """
    
    def __init__(self, in_features: int, out_features: int, 
                 alpha: float = 0.1, lambda_param: float = 0.5,
                 variant: bool = False, residual: bool = False):
        super().__init__()
        
        self.variant = variant
        self.residual = residual
        self.alpha = alpha
        self.lambda_param = lambda_param
        
        if variant:
            self.in_features = 2 * in_features
        else:
            self.in_features = in_features
            
        self.out_features = out_features
        self.weight = nn.Parameter(torch.FloatTensor(self.in_features, out_features))
        
        self.reset_parameters()
    
    def reset_parameters(self):
        stdv = 1. / np.sqrt(self.out_features)
        self.weight.data.uniform_(-stdv, stdv)
    
    def forward(self, input_feat: torch.Tensor, adj: torch.Tensor, 
                h0: torch.Tensor, layer_idx: int) -> torch.Tensor:
        """
        前向传播
        
        Args:
            input_feat: 输入特征
            adj: 邻接矩阵
            h0: 初始特征
            layer_idx: 层索引
            
        Returns:
            输出特征
        """
        theta = np.log(self.lambda_param / layer_idx + 1)
        hi = torch.spmm(adj, input_feat)
        
        if self.variant:
            support = torch.cat([hi, h0], 1)
            r = (1 - self.alpha) * hi + self.alpha * h0
        else:
            support = (1 - self.alpha) * hi + self.alpha * h0
            r = support
            
        output = theta * torch.mm(support, self.weight) + (1 - theta) * r
        
        if self.residual:
            output = output + input_feat
            
        return output
