"""
模型感知的解码器
真正利用模型学习结果的解码器
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Set
import logging

logger = logging.getLogger(__name__)


class ModelAwareConstrainedDecoder:
    """
    模型感知的约束解码器
    真正利用模型的预测能力，而不是纯规则生成
    """
    
    def __init__(self, model, token2idx, idx2token, emotion_categories, max_length=35):
        self.model = model
        self.token2idx = token2idx
        self.idx2token = idx2token
        self.max_length = max_length
        
        # 特殊token
        self.sos_id = token2idx.get('<sos>', 1)
        self.eos_id = token2idx.get('<eos>', 2)
        self.pad_id = token2idx.get('<pad>', 0)
        self.sep_id = token2idx.get('<sep>', 3)
        
        # 构建有效token集合
        self.emotion_tokens = set()
        self.emotion_ids = []
        for emotion in emotion_categories:
            if emotion in token2idx:
                self.emotion_tokens.add(emotion)
                self.emotion_ids.append(token2idx[emotion])
        
        # 构建话语ID映射
        self.utt_pattern_to_id = {}
        self.id_to_utt_pattern = {}
        for token, idx in token2idx.items():
            if token.startswith('utt_'):
                self.utt_pattern_to_id[token] = idx
                self.id_to_utt_pattern[idx] = token
        
        logger.info(f"初始化模型感知解码器: {len(self.emotion_tokens)}个情感, {len(self.utt_pattern_to_id)}个话语ID")
    
    def model_aware_decode(self, batch, use_constraints=True, temperature=1.0):
        """模型感知解码 - 真正利用模型的预测能力"""
        self.model.eval()
        device = next(self.model.parameters()).device
        
        with torch.no_grad():
            # 解析batch结构
            if len(batch) >= 6:
                utt_mask = batch[3]
                utt_speakers = batch[4]
                utt_emotions = batch[5]
            else:
                logger.warning("Batch结构不完整")
                return torch.empty((1, 0), device=device, dtype=torch.long)
            
            batch_size = utt_mask.size(0)
            all_predictions = []
            
            for b in range(batch_size):
                try:
                    # 获取有效话语数量
                    valid_utts = int(utt_mask[b].sum().item())
                    if valid_utts == 0:
                        pred = torch.tensor([self.sos_id, self.eos_id], device=device)
                        all_predictions.append(pred)
                        continue
                    
                    # 构建有效话语ID集合
                    valid_utt_ids = set()
                    for i in range(valid_utts):
                        utt_token = f'utt_{i:03d}'
                        if utt_token in self.token2idx:
                            valid_utt_ids.add(self.token2idx[utt_token])
                    
                    # 使用模型进行自回归解码
                    pred_sequence = self._autoregressive_decode_with_constraints(
                        batch, b, valid_utt_ids, use_constraints, temperature
                    )
                    
                    all_predictions.append(pred_sequence)
                    
                except Exception as e:
                    logger.warning(f"处理batch {b}时出错: {e}")
                    pred = torch.tensor([self.sos_id, self.eos_id], device=device)
                    all_predictions.append(pred)
            
            # 填充到相同长度
            if all_predictions:
                max_len = max(len(pred) for pred in all_predictions)
                padded_predictions = []
                
                for pred in all_predictions:
                    if len(pred) < max_len:
                        pad_length = max_len - len(pred)
                        padded_pred = torch.cat([
                            pred, 
                            torch.full((pad_length,), self.pad_id, device=pred.device)
                        ])
                    else:
                        padded_pred = pred
                    padded_predictions.append(padded_pred)
                
                return torch.stack(padded_predictions)
            else:
                return torch.empty((batch_size, 0), device=device, dtype=torch.long)
    
    def _autoregressive_decode_with_constraints(self, batch, batch_idx, valid_utt_ids, 
                                              use_constraints, temperature):
        """自回归解码with约束"""
        device = next(self.model.parameters()).device
        
        # 初始化序列
        sequence = [self.sos_id]
        current_state = 'START'
        
        # 获取模型的编码表示（这里需要根据实际模型结构调整）
        try:
            # 创建单样本batch用于解码
            single_batch = self._create_single_batch(batch, batch_idx)
            
            for step in range(self.max_length - 1):
                # 准备当前序列作为输入
                current_seq = torch.tensor([sequence], device=device)
                
                # 获取模型的下一个token预测
                logits = self._get_model_logits(single_batch, current_seq)
                
                if use_constraints:
                    # 应用约束
                    constrained_logits = self._apply_smart_constraints(
                        logits, current_state, sequence, valid_utt_ids
                    )
                else:
                    constrained_logits = logits
                
                # 温度采样
                if temperature > 0:
                    probs = F.softmax(constrained_logits / temperature, dim=-1)
                    next_token = torch.multinomial(probs, 1).item()
                else:
                    next_token = torch.argmax(constrained_logits).item()
                
                # 更新序列和状态
                sequence.append(next_token)
                current_state = self._get_next_state(current_state, next_token)
                
                # 检查结束条件
                if next_token == self.eos_id:
                    break
                
                # 防止无限循环
                if len(sequence) > self.max_length:
                    sequence.append(self.eos_id)
                    break
                    
        except Exception as e:
            logger.warning(f"自回归解码失败: {e}")
            # 回退到简单序列
            sequence = [self.sos_id, self.eos_id]
        
        return torch.tensor(sequence, device=device)
    
    def _get_model_logits(self, single_batch, current_seq):
        """获取模型对下一个token的预测logits - 完整实现"""
        try:
            # 保存当前模型状态
            was_training = self.model.training
            self.model.eval()

            with torch.no_grad():
                # 深度解析模型结构，获取真实的logits
                device = next(self.model.parameters()).device

                # 1. 提取图编码特征
                graph_features = self._extract_graph_features(single_batch)

                # 2. 构建解码器输入
                decoder_input = current_seq
                if decoder_input.size(1) == 0:
                    decoder_input = torch.tensor([[self.sos_id]], device=device)

                # 3. 获取解码器的隐藏状态
                hidden_states = self._get_decoder_hidden_states(graph_features, decoder_input)

                # 4. 计算下一个token的logits
                next_logits = self._compute_next_token_logits(hidden_states, graph_features)

                # 恢复模型状态
                if was_training:
                    self.model.train()

                return next_logits

        except Exception as e:
            logger.warning(f"获取模型logits失败: {e}")
            # 恢复模型状态
            if was_training:
                self.model.train()

            # 使用模型的实际前向传播作为备选
            return self._fallback_get_logits(single_batch, current_seq)

    def _extract_graph_features(self, batch):
        """提取图特征"""
        try:
            # 使用模型的图编码器
            if hasattr(self.model, 'graph_encoder'):
                # 构建图输入
                graph_data = self._prepare_graph_input(batch)
                graph_features = self.model.graph_encoder(graph_data)
                return graph_features
            else:
                # 如果没有图编码器，使用PLM特征
                return self._extract_plm_features(batch)
        except Exception as e:
            logger.warning(f"提取图特征失败: {e}")
            device = next(self.model.parameters()).device
            return torch.randn(1, 256, device=device)

    def _prepare_graph_input(self, batch):
        """准备图输入数据"""
        try:
            # 根据实际的batch结构构建图数据
            if len(batch) >= 6:
                dialog_tokens = batch[0]
                dialog_uttid = batch[1]
                dialog_mask = batch[2]
                utt_mask = batch[3]
                utt_speakers = batch[4]
                utt_emotions = batch[5]

                # 构建图数据结构
                graph_data = {
                    'dialog_tokens': dialog_tokens,
                    'dialog_uttid': dialog_uttid,
                    'dialog_mask': dialog_mask,
                    'utt_mask': utt_mask,
                    'utt_speakers': utt_speakers,
                    'utt_emotions': utt_emotions
                }
                return graph_data
            else:
                return {}
        except Exception as e:
            logger.warning(f"准备图输入失败: {e}")
            return {}

    def _extract_plm_features(self, batch):
        """提取PLM特征"""
        try:
            if hasattr(self.model, 'plm') and len(batch) > 0:
                dialog_tokens = batch[0]
                if torch.is_tensor(dialog_tokens):
                    # 重塑为PLM输入格式
                    batch_size, max_utts, max_words = dialog_tokens.shape
                    flat_tokens = dialog_tokens.view(-1, max_words)

                    # 通过PLM
                    plm_outputs = self.model.plm(flat_tokens)

                    # 获取pooled输出
                    if hasattr(plm_outputs, 'pooler_output'):
                        features = plm_outputs.pooler_output
                    else:
                        features = plm_outputs.last_hidden_state.mean(dim=1)

                    return features.view(batch_size, max_utts, -1).mean(dim=1)

            device = next(self.model.parameters()).device
            return torch.randn(1, 768, device=device)

        except Exception as e:
            logger.warning(f"提取PLM特征失败: {e}")
            device = next(self.model.parameters()).device
            return torch.randn(1, 768, device=device)

    def _get_decoder_hidden_states(self, graph_features, decoder_input):
        """获取解码器隐藏状态"""
        try:
            if hasattr(self.model, 'graph_decoder'):
                decoder = self.model.graph_decoder

                # 初始化解码器状态
                if hasattr(decoder, 'init_hidden'):
                    hidden = decoder.init_hidden(graph_features)
                else:
                    hidden = graph_features

                # 通过解码器RNN
                if hasattr(decoder, 'decoder') and hasattr(decoder.decoder, 'rnn'):
                    # 嵌入输入token
                    if hasattr(decoder, 'embedding'):
                        embedded = decoder.embedding(decoder_input)
                    else:
                        embedded = torch.randn_like(decoder_input, dtype=torch.float)

                    # RNN前向传播
                    output, hidden = decoder.decoder.rnn(embedded, hidden)
                    return output[:, -1, :]  # 最后一个时间步
                else:
                    return hidden
            else:
                return graph_features

        except Exception as e:
            logger.warning(f"获取解码器隐藏状态失败: {e}")
            device = next(self.model.parameters()).device
            return torch.randn(1, 256, device=device)

    def _compute_next_token_logits(self, hidden_states, graph_features):
        """计算下一个token的logits"""
        try:
            if hasattr(self.model, 'graph_decoder'):
                decoder = self.model.graph_decoder

                # 使用输出投影层
                if hasattr(decoder, 'out'):
                    logits = decoder.out(hidden_states)
                elif hasattr(decoder, 'output_projection'):
                    logits = decoder.output_projection(hidden_states)
                else:
                    # 创建临时投影层
                    vocab_size = len(self.token2idx)
                    hidden_size = hidden_states.size(-1)
                    projection = nn.Linear(hidden_size, vocab_size).to(hidden_states.device)
                    logits = projection(hidden_states)

                return logits.squeeze(0) if logits.dim() > 1 else logits
            else:
                # 默认投影
                vocab_size = len(self.token2idx)
                hidden_size = hidden_states.size(-1)
                device = hidden_states.device
                projection = nn.Linear(hidden_size, vocab_size).to(device)
                return projection(hidden_states).squeeze(0)

        except Exception as e:
            logger.warning(f"计算logits失败: {e}")
            vocab_size = len(self.token2idx)
            device = next(self.model.parameters()).device
            return torch.randn(vocab_size, device=device)

    def _fallback_get_logits(self, single_batch, current_seq):
        """备选方案：使用完整模型前向传播"""
        try:
            device = next(self.model.parameters()).device

            # 创建合适长度的目标序列
            target_length = 25
            if current_seq.size(1) < target_length:
                pad_length = target_length - current_seq.size(1)
                padded_seq = torch.cat([
                    current_seq,
                    torch.zeros(current_seq.size(0), pad_length, device=device, dtype=current_seq.dtype)
                ], dim=1)
            else:
                padded_seq = current_seq[:, :target_length]

            # 模型前向传播
            outputs, _ = self.model(single_batch, padded_seq, teacher_forcing_ratio=0.0)

            # 获取当前位置的logits
            current_pos = min(current_seq.size(1) - 1, outputs.size(1) - 1)
            current_pos = max(0, current_pos)

            return outputs[0, current_pos, :]

        except Exception as e:
            logger.warning(f"备选logits获取失败: {e}")
            vocab_size = len(self.token2idx)
            device = next(self.model.parameters()).device
            return torch.randn(vocab_size, device=device)
    
    def _apply_smart_constraints(self, logits, current_state, sequence, valid_utt_ids):
        """应用智能约束 - 完整版本"""
        constrained_logits = logits.clone()
        device = logits.device
        vocab_size = len(logits)

        # 创建精细化掩码
        constraint_mask = torch.zeros_like(logits)

        # 状态机约束
        if current_state == 'START':
            # 开始状态：只允许话语ID
            for token_id in valid_utt_ids:
                if token_id < vocab_size:
                    constraint_mask[token_id] = 1.0

        elif current_state == 'EMOTION_UTT':
            # 情感话语状态：只允许话语ID
            for token_id in valid_utt_ids:
                if token_id < vocab_size:
                    constraint_mask[token_id] = 1.0

        elif current_state == 'EMOTION':
            # 情感状态：只允许情感类别
            for emotion_id in self.emotion_ids:
                if emotion_id < vocab_size:
                    constraint_mask[emotion_id] = 1.0

        elif current_state == 'CAUSE_UTT':
            # 原因话语状态：允许话语ID
            for token_id in valid_utt_ids:
                if token_id < vocab_size:
                    constraint_mask[token_id] = 1.0

        elif current_state == 'SEP_OR_END':
            # 分隔或结束状态：允许分隔符和结束符
            if self.sep_id < vocab_size:
                constraint_mask[self.sep_id] = 1.0
            if self.eos_id < vocab_size:
                constraint_mask[self.eos_id] = 1.0

        else:
            # 未知状态：允许所有合理token
            constraint_mask.fill_(0.5)  # 中性权重

        # 应用约束（软约束，保留模型判断）
        constraint_strength = 3.0  # 约束强度
        constrained_logits = constrained_logits + (constraint_mask - 0.5) * constraint_strength

        # 高级约束策略
        constrained_logits = self._apply_advanced_constraints(
            constrained_logits, current_state, sequence, valid_utt_ids
        )

        # 重复惩罚和长度控制
        constrained_logits = self._apply_repetition_penalty(constrained_logits, sequence)

        # 语义一致性约束
        constrained_logits = self._apply_semantic_constraints(
            constrained_logits, current_state, sequence, valid_utt_ids
        )

        return constrained_logits

    def _apply_advanced_constraints(self, logits, current_state, sequence, valid_utt_ids):
        """应用高级约束策略"""
        try:
            # 1. 自指原因奖励
            if current_state == 'CAUSE_UTT' and len(sequence) >= 3:
                emotion_utt_token = sequence[-2]  # 情感话语
                if emotion_utt_token in valid_utt_ids and emotion_utt_token < len(logits):
                    logits[emotion_utt_token] += 1.0  # 奖励自指

            # 2. 邻近话语奖励
            if current_state == 'CAUSE_UTT' and len(sequence) >= 3:
                emotion_utt_token = sequence[-2]
                valid_utt_list = sorted(list(valid_utt_ids))

                if emotion_utt_token in valid_utt_list:
                    emotion_idx = valid_utt_list.index(emotion_utt_token)

                    # 奖励前后邻近话语
                    if emotion_idx > 0:
                        prev_utt = valid_utt_list[emotion_idx - 1]
                        if prev_utt < len(logits):
                            logits[prev_utt] += 0.5

                    if emotion_idx + 1 < len(valid_utt_list):
                        next_utt = valid_utt_list[emotion_idx + 1]
                        if next_utt < len(logits):
                            logits[next_utt] += 0.5

            # 3. 情感分布平衡
            if current_state == 'EMOTION':
                # 统计已生成的情感
                emotion_counts = {}
                for i in range(1, len(sequence), 4):  # 每4个token一个三元组
                    if i + 1 < len(sequence):
                        emotion_token = sequence[i + 1]
                        if emotion_token in self.emotion_ids:
                            emotion_counts[emotion_token] = emotion_counts.get(emotion_token, 0) + 1

                # 奖励少见情感
                for emotion_id in self.emotion_ids:
                    if emotion_id < len(logits):
                        count = emotion_counts.get(emotion_id, 0)
                        if count == 0:
                            logits[emotion_id] += 0.3  # 奖励未出现的情感
                        elif count == 1:
                            logits[emotion_id] += 0.1  # 轻微奖励少见情感

            return logits

        except Exception as e:
            logger.warning(f"应用高级约束失败: {e}")
            return logits

    def _apply_repetition_penalty(self, logits, sequence):
        """应用重复惩罚"""
        try:
            # 惩罚最近的token
            recent_tokens = sequence[-5:] if len(sequence) > 5 else sequence
            for token in recent_tokens:
                if token < len(logits):
                    logits[token] -= 0.5

            # 特别惩罚连续重复
            if len(sequence) >= 2 and sequence[-1] == sequence[-2]:
                if sequence[-1] < len(logits):
                    logits[sequence[-1]] -= 2.0

            # 长度控制
            if len(sequence) > 30:  # 序列过长时强制结束
                if self.eos_id < len(logits):
                    logits[self.eos_id] += 5.0

            return logits

        except Exception as e:
            logger.warning(f"应用重复惩罚失败: {e}")
            return logits

    def _apply_semantic_constraints(self, logits, current_state, sequence, valid_utt_ids):
        """应用语义一致性约束"""
        try:
            # 1. 确保三元组完整性
            if current_state == 'SEP_OR_END':
                # 检查当前三元组是否完整
                if len(sequence) >= 4:
                    last_triplet = sequence[-3:]
                    if (len(last_triplet) == 3 and
                        last_triplet[0] in valid_utt_ids and
                        last_triplet[1] in self.emotion_ids and
                        last_triplet[2] in valid_utt_ids):
                        # 完整三元组，可以分隔或结束
                        if self.sep_id < len(logits):
                            logits[self.sep_id] += 1.0
                        if self.eos_id < len(logits):
                            logits[self.eos_id] += 1.0

            # 2. 避免无效组合
            if current_state == 'CAUSE_UTT':
                # 避免原因话语与情感话语相同且情感为neutral
                if len(sequence) >= 2:
                    emotion_utt = sequence[-2]
                    emotion = sequence[-1]

                    # 如果是neutral情感，轻微惩罚自指
                    neutral_id = self.token2idx.get('neutral', -1)
                    if emotion == neutral_id and emotion_utt in valid_utt_ids:
                        if emotion_utt < len(logits):
                            logits[emotion_utt] -= 0.3

            # 3. 序列长度适应性
            triplet_count = (len(sequence) - 1) // 4  # 估算已生成的三元组数
            if triplet_count >= 3:  # 已有3个三元组
                # 倾向于结束
                if self.eos_id < len(logits):
                    logits[self.eos_id] += 0.5
            elif triplet_count >= 5:  # 已有5个三元组
                # 强烈倾向于结束
                if self.eos_id < len(logits):
                    logits[self.eos_id] += 2.0

            return logits

        except Exception as e:
            logger.warning(f"应用语义约束失败: {e}")
            return logits
    
    def _get_next_state(self, current_state, token):
        """获取下一个状态"""
        if current_state == 'START' and token in self.utt_pattern_to_id.values():
            return 'EMOTION'
        elif current_state == 'EMOTION_UTT' and token in self.utt_pattern_to_id.values():
            return 'EMOTION'
        elif current_state == 'EMOTION' and token in self.emotion_ids:
            return 'CAUSE_UTT'
        elif current_state == 'CAUSE_UTT' and token in self.utt_pattern_to_id.values():
            return 'SEP_OR_END'
        elif current_state == 'SEP_OR_END' and token == self.sep_id:
            return 'EMOTION_UTT'
        elif current_state == 'SEP_OR_END' and token == self.eos_id:
            return 'END'
        
        return current_state
    
    def _create_single_batch(self, batch, batch_idx):
        """创建单样本batch用于解码"""
        try:
            # 提取单个样本
            single_batch = []
            for item in batch:
                if torch.is_tensor(item):
                    if len(item.shape) > 0:
                        single_batch.append(item[batch_idx:batch_idx+1])
                    else:
                        single_batch.append(item)
                else:
                    single_batch.append(item)
            return single_batch
        except Exception as e:
            logger.warning(f"创建单样本batch失败: {e}")
            return batch
    
    def validate_sequence_quality(self, sequence):
        """验证序列质量"""
        tokens = [self.idx2token.get(idx.item() if torch.is_tensor(idx) else idx, '<unk>') 
                 for idx in sequence]
        
        # 基本格式检查
        has_sos = '<sos>' in tokens
        has_eos = '<eos>' in tokens
        
        # 解析情感-原因对
        pairs = []
        i = 1 if has_sos else 0  # 跳过SOS
        
        while i < len(tokens) - (1 if has_eos else 0):
            if (i + 2 < len(tokens) and 
                tokens[i].startswith('utt_') and 
                tokens[i + 1] in self.emotion_tokens and 
                tokens[i + 2].startswith('utt_')):
                
                pairs.append((tokens[i], tokens[i + 1], tokens[i + 2]))
                i += 3
                
                # 跳过分隔符
                if i < len(tokens) and tokens[i] == '<sep>':
                    i += 1
            else:
                i += 1
        
        return {
            'has_sos': has_sos,
            'has_eos': has_eos,
            'num_pairs': len(pairs),
            'pairs': pairs,
            'is_valid': has_sos and has_eos and len(pairs) > 0
        }


class ConsistentTrainingStrategy:
    """一致性训练策略 - 确保训练和推理一致"""
    
    def __init__(self, model, decoder):
        self.model = model
        self.decoder = decoder
    
    def consistent_forward(self, batch, target_seqs=None, teacher_forcing_ratio=0.5):
        """一致性前向传播 - 训练和推理使用相同的解码策略"""
        if target_seqs is not None and torch.rand(1).item() < teacher_forcing_ratio:
            # Teacher forcing模式 - 直接使用原始模型
            return self.model(batch, target_seqs, teacher_forcing_ratio=1.0)
        else:
            # 简化策略：在训练时也使用teacher forcing，但降低比例
            # 避免在训练过程中调用复杂的自回归解码
            if target_seqs is not None:
                return self.model(batch, target_seqs, teacher_forcing_ratio=0.3)
            else:
                # 如果没有目标序列，创建dummy序列
                batch_size = len(batch[0]) if isinstance(batch[0], (list, tuple)) else batch[0].size(0)
                device = next(self.model.parameters()).device
                dummy_target = torch.zeros(batch_size, 25, device=device, dtype=torch.long)
                return self.model(batch, dummy_target, teacher_forcing_ratio=0.0)
    
    def compute_consistent_loss(self, batch, target_seqs, teacher_forcing_ratio=0.5):
        """计算一致性损失"""
        try:
            # 确保模型在训练模式
            self.model.train()

            # 使用一致性前向传播
            outputs, _ = self.consistent_forward(batch, target_seqs, teacher_forcing_ratio)

            # 确保输出和目标的形状匹配
            if outputs.size(1) != target_seqs.size(1):
                min_len = min(outputs.size(1), target_seqs.size(1))
                outputs = outputs[:, :min_len, :]
                target_seqs = target_seqs[:, :min_len]

            # 计算损失
            criterion = nn.CrossEntropyLoss(ignore_index=self.decoder.pad_id)
            vocab_size = outputs.size(-1)
            loss = criterion(outputs.view(-1, vocab_size), target_seqs.view(-1))

            return loss

        except Exception as e:
            logger.warning(f"计算一致性损失失败: {e}")
            # 回退到简单的交叉熵损失
            outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=teacher_forcing_ratio)
            criterion = nn.CrossEntropyLoss(ignore_index=self.decoder.pad_id)
            vocab_size = outputs.size(-1)
            return criterion(outputs.view(-1, vocab_size), target_seqs.view(-1))
