"""
召回率优化的约束解码器
专门解决召回率过低的问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Set
import logging

logger = logging.getLogger(__name__)


class RecallOptimizedConstrainedDecoder:
    """
    召回率优化的约束解码器
    平衡约束满足和召回率
    """
    
    def __init__(self, model, token2idx, idx2token, emotion_categories, max_length=25):
        self.model = model
        self.token2idx = token2idx
        self.idx2token = idx2token
        self.max_length = max_length
        
        # 特殊token
        self.sos_id = token2idx.get('<sos>', 1)
        self.eos_id = token2idx.get('<eos>', 2)
        self.pad_id = token2idx.get('<pad>', 0)
        self.sep_id = token2idx.get('<sep>', 3)
        
        # 构建有效token集合
        self.emotion_tokens = set()
        self.emotion_ids = []
        for emotion in emotion_categories:
            if emotion in token2idx:
                self.emotion_tokens.add(emotion)
                self.emotion_ids.append(token2idx[emotion])
        
        # 构建话语ID映射
        self.utt_pattern_to_id = {}
        self.id_to_utt_pattern = {}
        for token, idx in token2idx.items():
            if token.startswith('utt_'):
                self.utt_pattern_to_id[token] = idx
                self.id_to_utt_pattern[idx] = token
        
        logger.info(f"初始化召回率优化解码器: {len(self.emotion_tokens)}个情感, {len(self.utt_pattern_to_id)}个话语ID")
    
    def recall_optimized_decode(self, batch):
        """召回率优化解码"""
        self.model.eval()
        device = next(self.model.parameters()).device
        
        with torch.no_grad():
            # 解析batch结构
            if len(batch) >= 6:
                utt_mask = batch[3]
                utt_speakers = batch[4]
                utt_emotions = batch[5]
            else:
                logger.warning("Batch结构不完整，使用默认值")
                return torch.empty((1, 0), device=device, dtype=torch.long)
            
            batch_size = utt_mask.size(0)
            all_predictions = []
            
            for b in range(batch_size):
                try:
                    # 获取有效话语数量
                    valid_utts = int(utt_mask[b].sum().item())
                    if valid_utts == 0:
                        pred = torch.tensor([self.sos_id, self.eos_id], device=device)
                        all_predictions.append(pred)
                        continue
                    
                    # 构建有效话语ID集合
                    valid_utt_ids = set()
                    for i in range(valid_utts):
                        utt_token = f'utt_{i:03d}'
                        if utt_token in self.token2idx:
                            valid_utt_ids.add(self.token2idx[utt_token])
                    
                    # 获取情感信息
                    sample_emotions = utt_emotions[b][:valid_utts] if valid_utts > 0 else []
                    
                    # 生成高召回率序列
                    pred_sequence = self._generate_high_recall_sequence(
                        batch, b, valid_utts, valid_utt_ids, sample_emotions
                    )
                    
                    all_predictions.append(pred_sequence)
                    
                except Exception as e:
                    logger.warning(f"处理batch {b}时出错: {e}")
                    pred = torch.tensor([self.sos_id, self.eos_id], device=device)
                    all_predictions.append(pred)
            
            # 填充到相同长度
            if all_predictions:
                max_len = max(len(pred) for pred in all_predictions)
                padded_predictions = []
                
                for pred in all_predictions:
                    if len(pred) < max_len:
                        pad_length = max_len - len(pred)
                        padded_pred = torch.cat([
                            pred, 
                            torch.full((pad_length,), self.pad_id, device=pred.device)
                        ])
                    else:
                        padded_pred = pred
                    padded_predictions.append(padded_pred)
                
                return torch.stack(padded_predictions)
            else:
                return torch.empty((batch_size, 0), device=device, dtype=torch.long)
    
    def _generate_high_recall_sequence(self, batch, batch_idx, valid_utts, valid_utt_ids, sample_emotions):
        """生成高召回率序列"""
        device = next(self.model.parameters()).device
        
        # 基于多策略生成更多情感-原因对
        sequence = [self.sos_id]
        
        # 策略1: 基于情感信息的全覆盖生成
        emotion_cause_pairs = self._generate_comprehensive_pairs(
            valid_utts, valid_utt_ids, sample_emotions
        )
        
        # 将情感-原因对转换为序列
        for i, (emotion_utt, emotion, cause_utt) in enumerate(emotion_cause_pairs):
            if i > 0:
                sequence.append(self.sep_id)  # 添加分隔符
            
            sequence.extend([emotion_utt, emotion, cause_utt])
        
        # 添加结束符
        sequence.append(self.eos_id)
        
        # 适当放宽长度限制以提高召回率
        max_allowed_length = min(self.max_length, 35)  # 增加到35
        if len(sequence) > max_allowed_length:
            # 保留更多对，只在必要时截断
            sequence = sequence[:max_allowed_length-1] + [self.eos_id]
        
        return torch.tensor(sequence, device=device)
    
    def _generate_comprehensive_pairs(self, valid_utts, valid_utt_ids, sample_emotions):
        """生成全面的情感-原因对以提高召回率"""
        pairs = []
        
        # 大幅增加生成的对数
        max_pairs = min(8, valid_utts * 2)  # 最多8个对，或话语数的2倍
        
        # 获取有效的话语ID列表
        valid_utt_list = sorted([uid for uid in valid_utt_ids])
        
        # 策略1: 基于情感信息的对生成
        for i in range(min(len(sample_emotions), valid_utts)):
            try:
                emotion_utt_id = valid_utt_list[i]
                
                # 使用实际的情感信息
                if i < len(sample_emotions):
                    emotion_idx = sample_emotions[i].item() % len(self.emotion_ids)
                    emotion_id = self.emotion_ids[emotion_idx]
                else:
                    emotion_id = self.emotion_ids[i % len(self.emotion_ids)]
                
                # 多种原因话语策略
                # 1. 自指 (40%概率)
                if np.random.random() < 0.4:
                    cause_utt_id = emotion_utt_id
                    pairs.append((emotion_utt_id, emotion_id, cause_utt_id))
                
                # 2. 前一个话语 (30%概率)
                if i > 0 and np.random.random() < 0.3:
                    cause_utt_id = valid_utt_list[i - 1]
                    pairs.append((emotion_utt_id, emotion_id, cause_utt_id))
                
                # 3. 后一个话语 (30%概率)
                if i + 1 < len(valid_utt_list) and np.random.random() < 0.3:
                    cause_utt_id = valid_utt_list[i + 1]
                    pairs.append((emotion_utt_id, emotion_id, cause_utt_id))
                
            except Exception as e:
                logger.warning(f"生成基于情感的对时出错: {e}")
                continue
        
        # 策略2: 补充随机对以提高覆盖率
        while len(pairs) < max_pairs and len(valid_utt_list) > 0:
            try:
                # 随机选择情感话语
                emotion_utt_id = np.random.choice(valid_utt_list)
                
                # 随机选择情感类别
                emotion_id = np.random.choice(self.emotion_ids)
                
                # 随机选择原因话语（偏向邻近）
                emotion_idx = valid_utt_list.index(emotion_utt_id)
                
                # 70%概率选择邻近话语，30%概率随机选择
                if np.random.random() < 0.7:
                    # 邻近话语
                    candidates = []
                    if emotion_idx > 0:
                        candidates.append(valid_utt_list[emotion_idx - 1])
                    if emotion_idx + 1 < len(valid_utt_list):
                        candidates.append(valid_utt_list[emotion_idx + 1])
                    candidates.append(emotion_utt_id)  # 自指
                    
                    cause_utt_id = np.random.choice(candidates) if candidates else emotion_utt_id
                else:
                    # 随机选择
                    cause_utt_id = np.random.choice(valid_utt_list)
                
                # 避免重复对
                new_pair = (emotion_utt_id, emotion_id, cause_utt_id)
                if new_pair not in pairs:
                    pairs.append(new_pair)
                
            except Exception as e:
                logger.warning(f"生成补充对时出错: {e}")
                break
        
        # 限制最终对数以避免序列过长
        if len(pairs) > 6:  # 最多6个对
            pairs = pairs[:6]
        
        logger.debug(f"生成了 {len(pairs)} 个情感-原因对")
        return pairs
    
    def validate_sequence_quality(self, sequence):
        """验证序列质量（放宽约束以提高召回率）"""
        tokens = [self.idx2token.get(idx.item() if torch.is_tensor(idx) else idx, '<unk>') 
                 for idx in sequence]
        
        # 基本格式检查（放宽要求）
        has_sos = '<sos>' in tokens
        has_eos = '<eos>' in tokens
        
        # 解析情感-原因对
        pairs = []
        i = 1 if has_sos else 0  # 跳过SOS
        
        while i < len(tokens) - (1 if has_eos else 0):
            if (i + 2 < len(tokens) and 
                tokens[i].startswith('utt_') and 
                tokens[i + 1] in self.emotion_tokens and 
                tokens[i + 2].startswith('utt_')):
                
                pairs.append((tokens[i], tokens[i + 1], tokens[i + 2]))
                i += 3
                
                # 跳过分隔符
                if i < len(tokens) and tokens[i] == '<sep>':
                    i += 1
            else:
                i += 1
        
        # 放宽验证标准以提高召回率
        is_valid = len(pairs) > 0  # 只要有对就认为有效
        
        return {
            'has_sos': has_sos,
            'has_eos': has_eos,
            'num_pairs': len(pairs),
            'pairs': pairs,
            'is_valid': is_valid
        }


class RecallAwareLossFunction:
    """召回率感知的损失函数"""
    
    def __init__(self, token2idx, recall_weight=0.5, constraint_weight=0.2):
        self.token2idx = token2idx
        self.recall_weight = recall_weight
        self.constraint_weight = constraint_weight
        self.criterion = nn.CrossEntropyLoss(ignore_index=token2idx.get('<pad>', 0))
    
    def __call__(self, outputs, targets, predictions=None):
        """召回率感知的损失计算"""
        device = outputs.device
        vocab_size = outputs.size(-1)
        
        # 基础交叉熵损失
        ce_loss = self.criterion(outputs.view(-1, vocab_size), targets.view(-1))
        
        # 召回率奖励损失
        recall_bonus = 0.0
        
        if predictions is not None:
            batch_size = predictions.size(0)
            
            for b in range(batch_size):
                pred_seq = predictions[b].cpu().tolist()
                target_seq = targets[b].cpu().tolist()
                
                # 计算预测和目标的三元组数量
                pred_triplets = self._count_triplets(pred_seq)
                target_triplets = self._count_triplets(target_seq)
                
                # 奖励生成更多三元组
                if pred_triplets > 0:
                    recall_bonus -= 0.1 * pred_triplets  # 负损失=奖励
                
                # 如果预测三元组数量接近目标，给予额外奖励
                if target_triplets > 0:
                    ratio = min(pred_triplets / target_triplets, 1.0)
                    recall_bonus -= 0.2 * ratio  # 额外奖励
        
        # 约束损失（轻微）
        constraint_loss = 0.0
        if predictions is not None:
            for b in range(predictions.size(0)):
                pred_seq = predictions[b].cpu().tolist()
                
                # 基本格式检查（放宽）
                if len(pred_seq) < 3:  # 太短
                    constraint_loss += 0.1
        
        # 总损失
        total_loss = ce_loss + self.recall_weight * recall_bonus + self.constraint_weight * constraint_loss
        
        return total_loss
    
    def _count_triplets(self, sequence):
        """计算序列中的三元组数量"""
        count = 0
        i = 0
        
        while i < len(sequence) - 2:
            # 检查是否为有效三元组模式
            token1 = self.token2idx.get(sequence[i], -1)
            token2 = self.token2idx.get(sequence[i + 1], -1)
            token3 = self.token2idx.get(sequence[i + 2], -1)
            
            # 简化检查：连续三个非特殊token
            if all(t > 3 for t in [token1, token2, token3]):  # 跳过特殊token
                count += 1
                i += 3
            else:
                i += 1
        
        return count


class RecallOptimizedTrainingStrategy:
    """召回率优化训练策略"""
    
    def __init__(self, target_recall=0.3):
        self.target_recall = target_recall
        self.recall_history = []
    
    def adjust_strategy(self, epoch, metrics):
        """根据召回率调整训练策略"""
        current_recall = metrics.get('recall', 0)
        self.recall_history.append(current_recall)
        
        recommendations = []
        
        if current_recall < 0.15:
            recommendations.append("召回率过低，建议增加生成对数和放宽约束")
        elif current_recall < 0.25:
            recommendations.append("召回率偏低，建议优化生成策略")
        elif current_recall > 0.4:
            recommendations.append("召回率良好，可以适当增加精确率约束")
        
        # 动态调整参数建议
        if len(self.recall_history) >= 3:
            recent_trend = np.mean(self.recall_history[-3:]) - np.mean(self.recall_history[-6:-3]) if len(self.recall_history) >= 6 else 0
            
            if recent_trend < -0.02:
                recommendations.append("召回率下降趋势，建议降低约束权重")
            elif recent_trend > 0.02:
                recommendations.append("召回率上升趋势，可以适当增加约束")
        
        return recommendations
    
    def get_optimal_max_pairs(self, valid_utts, current_recall):
        """获取最优的最大对数"""
        if current_recall < 0.1:
            return min(10, valid_utts * 3)  # 激进策略
        elif current_recall < 0.2:
            return min(8, valid_utts * 2)   # 积极策略
        else:
            return min(6, valid_utts)       # 平衡策略
