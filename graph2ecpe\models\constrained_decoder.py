"""
约束序列解码器
专门为ECPE任务设计的约束解码机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Tuple, Optional, Set
import re


class ECPEConstraintManager:
    """
    ECPE任务的约束管理器
    管理情感原因对提取的各种约束条件
    """
    
    def __init__(self, vocab, emotion_categories, max_utterances=50):
        self.vocab = vocab
        self.emotion_categories = emotion_categories
        self.max_utterances = max_utterances
        
        # 构建词汇映射
        self.token2idx = {token: idx for idx, token in enumerate(vocab)}
        self.idx2token = {idx: token for token, idx in self.token2idx.items()}
        
        # 特殊token
        self.SOS_token = self.token2idx.get('<sos>', 0)
        self.EOS_token = self.token2idx.get('<eos>', 1)
        self.SEP_token = self.token2idx.get('<sep>', 2)
        self.PAD_token = self.token2idx.get('<pad>', 3)
        
        # 构建约束规则
        self._build_constraint_rules()
    
    def _build_constraint_rules(self):
        """构建约束规则"""
        # 1. 话语ID模式
        self.utterance_pattern = re.compile(r'utt_(\d+)')
        
        # 2. 有效的话语ID token
        self.valid_utterance_tokens = set()
        for token in self.vocab:
            if self.utterance_pattern.match(token):
                self.valid_utterance_tokens.add(self.token2idx[token])
        
        # 3. 情感类别token
        self.emotion_tokens = set()
        for emotion in self.emotion_categories:
            if emotion in self.token2idx:
                self.emotion_tokens.add(self.token2idx[emotion])
        
        # 4. 状态转移规则
        self.build_state_transition_rules()
    
    def build_state_transition_rules(self):
        """构建状态转移规则"""
        # ECPE序列的状态机
        # 状态: START -> EMOTION_UTT -> EMOTION -> CAUSE_UTT -> SEP -> ...
        self.states = {
            'START': 0,           # 开始状态
            'EMOTION_UTT': 1,     # 期待情感话语ID
            'EMOTION': 2,         # 期待情感类别
            'CAUSE_UTT': 3,       # 期待原因话语ID
            'SEP_OR_END': 4,      # 期待分隔符或结束
        }
        
        # 每个状态允许的token类型
        self.state_allowed_tokens = {
            'START': self.valid_utterance_tokens,
            'EMOTION_UTT': self.valid_utterance_tokens,
            'EMOTION': self.emotion_tokens,
            'CAUSE_UTT': self.valid_utterance_tokens,
            'SEP_OR_END': {self.SEP_token, self.EOS_token}
        }
    
    def get_valid_tokens(self, current_state: str, generated_sequence: List[int], 
                        dialogue_utterances: Set[int]) -> Set[int]:
        """
        获取当前状态下的有效token
        
        Args:
            current_state: 当前状态
            generated_sequence: 已生成的序列
            dialogue_utterances: 对话中的有效话语ID
            
        Returns:
            valid_tokens: 有效token的集合
        """
        base_valid = self.state_allowed_tokens.get(current_state, set())
        
        # 进一步约束
        if current_state in ['EMOTION_UTT', 'CAUSE_UTT']:
            # 只允许对话中实际存在的话语ID
            valid_utterances = base_valid.intersection(dialogue_utterances)
            return valid_utterances
        
        return base_valid
    
    def get_next_state(self, current_state: str, token: int) -> str:
        """获取下一个状态"""
        if current_state == 'START' and token in self.valid_utterance_tokens:
            return 'EMOTION'
        elif current_state == 'EMOTION_UTT' and token in self.valid_utterance_tokens:
            return 'EMOTION'
        elif current_state == 'EMOTION' and token in self.emotion_tokens:
            return 'CAUSE_UTT'
        elif current_state == 'CAUSE_UTT' and token in self.valid_utterance_tokens:
            return 'SEP_OR_END'
        elif current_state == 'SEP_OR_END' and token == self.SEP_token:
            return 'EMOTION_UTT'
        elif current_state == 'SEP_OR_END' and token == self.EOS_token:
            return 'END'
        
        return current_state  # 保持当前状态
    
    def apply_constraints(self, logits: torch.Tensor, current_state: str, 
                         generated_sequence: List[int], 
                         dialogue_utterances: Set[int]) -> torch.Tensor:
        """
        应用约束到logits
        
        Args:
            logits: 原始logits [vocab_size]
            current_state: 当前状态
            generated_sequence: 已生成序列
            dialogue_utterances: 对话中的有效话语ID
            
        Returns:
            constrained_logits: 约束后的logits
        """
        constrained_logits = logits.clone()
        
        # 获取有效token
        valid_tokens = self.get_valid_tokens(current_state, generated_sequence, dialogue_utterances)
        
        # 创建掩码
        mask = torch.full_like(logits, float('-inf'))
        for token_idx in valid_tokens:
            if token_idx < len(mask):
                mask[token_idx] = 0
        
        # 应用掩码
        constrained_logits = logits + mask
        
        # 额外约束
        constrained_logits = self._apply_additional_constraints(
            constrained_logits, current_state, generated_sequence, dialogue_utterances
        )
        
        return constrained_logits
    
    def _apply_additional_constraints(self, logits: torch.Tensor, current_state: str,
                                    generated_sequence: List[int], 
                                    dialogue_utterances: Set[int]) -> torch.Tensor:
        """应用额外约束"""
        # 1. 重复惩罚
        if len(generated_sequence) > 0:
            for token in generated_sequence[-3:]:  # 惩罚最近3个token的重复
                if token < len(logits):
                    logits[token] -= 1.0
        
        # 2. 长度约束
        if len(generated_sequence) > 50:  # 防止序列过长
            logits[self.EOS_token] += 2.0
        
        # 3. 情感-原因对的语义约束
        if current_state == 'CAUSE_UTT' and len(generated_sequence) >= 2:
            emotion_utt_token = generated_sequence[-2]
            # 如果情感话语和原因话语相同，给予奖励
            if emotion_utt_token in dialogue_utterances:
                logits[emotion_utt_token] += 0.5
        
        return logits


class ConstrainedGraph2SeqDecoder(nn.Module):
    """
    约束图到序列解码器
    集成ECPE任务特定的约束机制
    """
    
    def __init__(self, hidden_size, vocab_size, vocab, emotion_categories,
                 num_layers=2, dropout=0.1, max_length=100):
        super().__init__()
        
        self.hidden_size = hidden_size
        self.vocab_size = vocab_size
        self.num_layers = num_layers
        self.max_length = max_length
        
        # 约束管理器
        self.constraint_manager = ECPEConstraintManager(vocab, emotion_categories)
        
        # 解码器组件
        self.embedding = nn.Embedding(vocab_size, hidden_size, padding_idx=0)
        self.attention = AttentionLayer(hidden_size)
        self.gru = nn.GRU(hidden_size * 2, hidden_size, num_layers=num_layers,
                         dropout=(dropout if num_layers > 1 else 0), batch_first=True)
        self.out = nn.Linear(hidden_size * 2, vocab_size)
        self.bridge = nn.Linear(hidden_size, hidden_size * num_layers)
        
        # 特殊token
        self.SOS_token = self.constraint_manager.SOS_token
        self.EOS_token = self.constraint_manager.EOS_token
        self.SEP_token = self.constraint_manager.SEP_token
    
    def forward(self, node_features, graph_emb, target_seq=None, 
                dialogue_utterances=None, teacher_forcing_ratio=0.5,
                use_constraints=True):
        """
        约束解码前向传播
        
        Args:
            node_features: 节点特征 [num_nodes, hidden_size]
            graph_emb: 图嵌入 [batch_size, hidden_size]
            target_seq: 目标序列 [batch_size, seq_len] (可选)
            dialogue_utterances: 每个batch的有效话语ID列表
            teacher_forcing_ratio: teacher forcing比例
            use_constraints: 是否使用约束
        """
        device = node_features.device
        batch_size = graph_emb.size(0)
        
        # 处理对话话语ID
        if dialogue_utterances is None:
            dialogue_utterances = [set(range(20)) for _ in range(batch_size)]  # 默认值
        
        # 初始化解码器状态
        bridge_output = self.bridge(graph_emb)
        decoder_hidden = bridge_output.view(self.num_layers, batch_size, self.hidden_size)
        
        # 初始输入
        decoder_input = torch.full((batch_size, 1), self.SOS_token, dtype=torch.long, device=device)
        
        # 确定序列长度
        max_target_length = self.max_length
        if target_seq is not None:
            max_target_length = target_seq.size(1)
        
        # 初始化输出
        decoder_outputs = torch.zeros(batch_size, max_target_length, self.vocab_size, device=device)
        attention_weights = torch.zeros(batch_size, max_target_length, node_features.size(0), device=device)
        
        # 初始化状态跟踪
        current_states = ['START'] * batch_size
        generated_sequences = [[] for _ in range(batch_size)]
        
        # 逐步解码
        for t in range(max_target_length):
            # 单步解码
            decoder_output, decoder_hidden, attn_weights = self._decode_step(
                decoder_input, decoder_hidden, node_features
            )
            
            # 应用约束
            if use_constraints:
                for b in range(batch_size):
                    decoder_output[b] = self.constraint_manager.apply_constraints(
                        decoder_output[b], 
                        current_states[b],
                        generated_sequences[b],
                        dialogue_utterances[b]
                    )
            
            # 存储输出
            decoder_outputs[:, t, :] = decoder_output
            attention_weights[:, t, :] = attn_weights
            
            # 准备下一步输入
            if target_seq is not None and torch.rand(1).item() < teacher_forcing_ratio and t < max_target_length - 1:
                # Teacher forcing
                decoder_input = target_seq[:, t+1].unsqueeze(1)
                next_tokens = target_seq[:, t+1].cpu().tolist()
            else:
                # 自由生成
                topv, topi = decoder_output.topk(1)
                decoder_input = topi.detach()
                next_tokens = topi.squeeze(-1).cpu().tolist()
            
            # 更新状态
            for b in range(batch_size):
                token = next_tokens[b]
                generated_sequences[b].append(token)
                current_states[b] = self.constraint_manager.get_next_state(
                    current_states[b], token
                )
        
        return decoder_outputs, attention_weights
    
    def _decode_step(self, input_token, hidden, node_features):
        """单步解码"""
        # 嵌入
        embedded = self.embedding(input_token)  # [batch_size, 1, hidden_size]
        
        # 注意力
        attn_weights = self.attention(hidden[-1], node_features)  # [batch_size, num_nodes]
        context = torch.bmm(attn_weights.unsqueeze(1), node_features.unsqueeze(0).expand(
            attn_weights.size(0), -1, -1))  # [batch_size, 1, hidden_size]
        
        # 拼接输入和上下文
        rnn_input = torch.cat([embedded, context], dim=2)  # [batch_size, 1, hidden_size*2]
        
        # GRU
        output, hidden = self.gru(rnn_input, hidden)
        
        # 输出投影
        output = torch.cat([output.squeeze(1), context.squeeze(1)], dim=1)
        output = self.out(output)  # [batch_size, vocab_size]
        
        return output, hidden, attn_weights.squeeze(1)


class AttentionLayer(nn.Module):
    """注意力层"""
    
    def __init__(self, hidden_size):
        super().__init__()
        self.hidden_size = hidden_size
        self.attn = nn.Linear(hidden_size * 2, hidden_size)
        self.v = nn.Parameter(torch.rand(hidden_size))
    
    def forward(self, hidden, encoder_outputs):
        """
        Args:
            hidden: [batch_size, hidden_size]
            encoder_outputs: [num_nodes, hidden_size]
        """
        batch_size = hidden.size(0)
        seq_len = encoder_outputs.size(0)
        
        # 扩展hidden以匹配encoder_outputs
        hidden_expanded = hidden.unsqueeze(1).expand(batch_size, seq_len, self.hidden_size)
        encoder_expanded = encoder_outputs.unsqueeze(0).expand(batch_size, seq_len, self.hidden_size)
        
        # 计算注意力能量
        energy = torch.tanh(self.attn(torch.cat([hidden_expanded, encoder_expanded], dim=2)))
        energy = energy.transpose(1, 2)  # [batch_size, hidden_size, seq_len]
        
        # 计算注意力权重
        v_expanded = self.v.unsqueeze(0).unsqueeze(0).expand(batch_size, 1, self.hidden_size)
        attention = torch.bmm(v_expanded, energy).squeeze(1)  # [batch_size, seq_len]
        
        return F.softmax(attention, dim=1)


class BeamSearchConstrainedDecoder:
    """
    束搜索约束解码器
    结合约束条件的束搜索解码
    """

    def __init__(self, decoder, constraint_manager, beam_size=5):
        self.decoder = decoder
        self.constraint_manager = constraint_manager
        self.beam_size = beam_size

    def decode(self, node_features, graph_emb, dialogue_utterances, max_length=50):
        """
        约束束搜索解码

        Args:
            node_features: 节点特征
            graph_emb: 图嵌入
            dialogue_utterances: 有效话语ID集合
            max_length: 最大长度

        Returns:
            best_sequences: 最佳序列列表
            best_scores: 对应分数
        """
        device = node_features.device
        batch_size = graph_emb.size(0)

        # 初始化束
        beams = []
        for b in range(batch_size):
            beam = {
                'sequence': [self.constraint_manager.SOS_token],
                'score': 0.0,
                'state': 'START',
                'hidden': None
            }
            beams.append([beam])

        # 初始化解码器状态
        bridge_output = self.decoder.bridge(graph_emb)
        initial_hidden = bridge_output.view(self.decoder.num_layers, batch_size, self.decoder.hidden_size)

        for b in range(batch_size):
            beams[b][0]['hidden'] = initial_hidden[:, b:b+1, :].contiguous()

        # 束搜索解码
        for step in range(max_length):
            new_beams = []

            for b in range(batch_size):
                candidates = []

                for beam in beams[b]:
                    if beam['sequence'][-1] == self.constraint_manager.EOS_token:
                        candidates.append(beam)
                        continue

                    # 解码一步
                    input_token = torch.tensor([[beam['sequence'][-1]]], device=device)
                    output, hidden, _ = self.decoder._decode_step(
                        input_token, beam['hidden'], node_features
                    )

                    # 应用约束
                    constrained_output = self.constraint_manager.apply_constraints(
                        output[0], beam['state'], beam['sequence'], dialogue_utterances[b]
                    )

                    # 获取top-k候选
                    log_probs = F.log_softmax(constrained_output, dim=-1)
                    top_scores, top_indices = log_probs.topk(self.beam_size)

                    for score, token_idx in zip(top_scores, top_indices):
                        new_sequence = beam['sequence'] + [token_idx.item()]
                        new_score = beam['score'] + score.item()
                        new_state = self.constraint_manager.get_next_state(
                            beam['state'], token_idx.item()
                        )

                        candidates.append({
                            'sequence': new_sequence,
                            'score': new_score,
                            'state': new_state,
                            'hidden': hidden
                        })

                # 选择最佳候选
                candidates.sort(key=lambda x: x['score'], reverse=True)
                new_beams.append(candidates[:self.beam_size])

            beams = new_beams

        # 提取最佳序列
        best_sequences = []
        best_scores = []

        for b in range(batch_size):
            best_beam = max(beams[b], key=lambda x: x['score'])
            best_sequences.append(best_beam['sequence'])
            best_scores.append(best_beam['score'])

        return best_sequences, best_scores


class ECPESequenceValidator:
    """
    ECPE序列验证器
    验证生成序列的正确性
    """

    def __init__(self, vocab, emotion_categories):
        self.vocab = vocab
        self.emotion_categories = emotion_categories
        self.token2idx = {token: idx for idx, token in enumerate(vocab)}
        self.idx2token = {idx: token for token, idx in self.token2idx.items()}

    def validate_sequence(self, sequence: List[int]) -> Dict:
        """
        验证序列的正确性

        Args:
            sequence: token索引序列

        Returns:
            validation_result: 验证结果字典
        """
        tokens = [self.idx2token.get(idx, '<UNK>') for idx in sequence]

        result = {
            'is_valid': True,
            'errors': [],
            'emotion_cause_pairs': [],
            'statistics': {}
        }

        # 解析情感-原因对
        pairs = self._parse_emotion_cause_pairs(tokens)
        result['emotion_cause_pairs'] = pairs

        # 验证格式
        format_errors = self._validate_format(tokens)
        result['errors'].extend(format_errors)

        # 验证语义
        semantic_errors = self._validate_semantics(pairs)
        result['errors'].extend(semantic_errors)

        # 统计信息
        result['statistics'] = self._compute_statistics(tokens, pairs)

        result['is_valid'] = len(result['errors']) == 0

        return result

    def _parse_emotion_cause_pairs(self, tokens: List[str]) -> List[Tuple]:
        """解析情感-原因对"""
        pairs = []
        i = 0

        while i < len(tokens):
            if tokens[i].startswith('utt_'):
                # 寻找完整的三元组
                if i + 2 < len(tokens):
                    emotion_utt = tokens[i]
                    emotion = tokens[i + 1]
                    cause_utt = tokens[i + 2]

                    if (emotion in self.emotion_categories and
                        cause_utt.startswith('utt_')):
                        pairs.append((emotion_utt, emotion, cause_utt))
                        i += 3
                    else:
                        i += 1
                else:
                    i += 1
            else:
                i += 1

        return pairs

    def _validate_format(self, tokens: List[str]) -> List[str]:
        """验证格式"""
        errors = []

        # 检查是否以SOS开始
        if not tokens or tokens[0] != '<sos>':
            errors.append("序列应以<sos>开始")

        # 检查是否以EOS结束
        if not tokens or tokens[-1] != '<eos>':
            errors.append("序列应以<eos>结束")

        # 检查分隔符使用
        sep_count = tokens.count('<sep>')
        if sep_count == 0 and len(tokens) > 4:  # 如果有多个对，应该有分隔符
            errors.append("多个情感-原因对之间应使用<sep>分隔")

        return errors

    def _validate_semantics(self, pairs: List[Tuple]) -> List[str]:
        """验证语义"""
        errors = []

        if not pairs:
            errors.append("未找到有效的情感-原因对")

        for i, (emotion_utt, emotion, cause_utt) in enumerate(pairs):
            # 检查情感类别
            if emotion not in self.emotion_categories:
                errors.append(f"第{i+1}个对中的情感'{emotion}'不在有效类别中")

            # 检查话语ID格式
            if not emotion_utt.startswith('utt_'):
                errors.append(f"第{i+1}个对中的情感话语ID格式错误: {emotion_utt}")

            if not cause_utt.startswith('utt_'):
                errors.append(f"第{i+1}个对中的原因话语ID格式错误: {cause_utt}")

        return errors

    def _compute_statistics(self, tokens: List[str], pairs: List[Tuple]) -> Dict:
        """计算统计信息"""
        return {
            'sequence_length': len(tokens),
            'num_pairs': len(pairs),
            'unique_emotions': len(set(pair[1] for pair in pairs)),
            'unique_utterances': len(set(pair[0] for pair in pairs) |
                                   set(pair[2] for pair in pairs)),
            'self_cause_pairs': sum(1 for pair in pairs if pair[0] == pair[2])
        }
