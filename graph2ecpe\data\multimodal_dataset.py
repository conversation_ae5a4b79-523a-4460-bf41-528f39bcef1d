"""
多模态对话数据集加载器
支持HDF5格式的MELD和IEMOCAP多模态数据集
借鉴MMGCN的多模态处理策略
"""

import h5py
import torch
import numpy as np
from torch.utils.data import Dataset
from transformers import AutoTokenizer
from typing import Dict, List, Tuple, Optional, Union
import logging

from .dataset import DialogDataset, EDGE_TYPE_EMOTION_CAUSE, EDGE_TYPE_TEMPORAL_FORWARD, EDGE_TYPE_TEMPORAL_BACKWARD
from .dataset import EDGE_TYPE_SAME_SPEAKER, EDGE_TYPE_DIFF_SPEAKER, EDGE_TYPE_SAME_EMOTION
from utils.UtteranceItem import UtteranceItem
from utils.EmotionCauseLink import EmotionCauseLink

logger = logging.getLogger(__name__)


class MultimodalDialogDataset(Dataset):
    """
    多模态对话数据集
    支持文本、视觉、音频三种模态的融合处理
    """
    
    def __init__(self, 
                 h5_file_path: str,
                 tokenizer: str = "roberta",
                 input_length: int = 512,
                 window_past: int = 3,
                 window_future: int = 3,
                 emotion_categories: List[str] = None,
                 speakers: List[str] = None,
                 modalities: List[str] = ['text', 'visual', 'audio'],
                 visual_dim: int = 4096,
                 audio_dim: int = 6373,
                 normalize_features: bool = True,
                 use_speaker_embedding: bool = True,
                 use_modal_embedding: bool = True):
        """
        初始化多模态对话数据集
        
        Args:
            h5_file_path: HDF5数据文件路径
            tokenizer: 分词器名称
            input_length: 输入序列最大长度
            window_past: 过去窗口大小
            window_future: 未来窗口大小
            emotion_categories: 情感类别列表
            speakers: 说话者列表
            modalities: 使用的模态列表
            visual_dim: 视觉特征维度
            audio_dim: 音频特征维度
            normalize_features: 是否归一化特征
            use_speaker_embedding: 是否使用说话者嵌入
            use_modal_embedding: 是否使用模态嵌入
        """
        super().__init__()
        
        self.h5_file_path = h5_file_path
        self.modalities = modalities
        self.visual_dim = visual_dim
        self.audio_dim = audio_dim
        self.normalize_features = normalize_features
        self.use_speaker_embedding = use_speaker_embedding
        self.use_modal_embedding = use_modal_embedding
        
        # 窗口参数
        self.window_past = window_past
        self.window_future = window_future
        
        # 初始化分词器
        if isinstance(tokenizer, str):
            self.tokenizer = AutoTokenizer.from_pretrained(f"/userdata2/fengweijie/{tokenizer}")
        else:
            self.tokenizer = tokenizer
            
        # 特殊token
        self.CLS = self.tokenizer.cls_token_id
        self.SEP = self.tokenizer.sep_token_id
        self.PAD = self.tokenizer.pad_token_id
        
        # 情感和说话者映射
        if emotion_categories is None:
            emotion_categories = ["_NONE", "surprise", "joy", "sadness", "neutral", "disgust", "anger", "fear"]
        if speakers is None:
            speakers = ["_NONE", "Chandler", "Joey", "Ross", "Rachel", "Monica", "Phoebe"]
            
        self.em2i = {em: i for i, em in enumerate(emotion_categories)}
        self.sp2i = {sp: i for i, sp in enumerate(speakers)}
        
        # 加载对话数据
        self.conversations = self._load_conversations()
        
        logger.info(f"加载了 {len(self.conversations)} 个对话")
        logger.info(f"使用模态: {self.modalities}")
        
    def _load_conversations(self) -> List[Dict]:
        """从HDF5文件加载对话数据"""
        conversations = []
        
        with h5py.File(self.h5_file_path, 'r') as f:
            conversation_keys = [k for k in f.keys() if k.startswith('conversation_')]
            
            for conv_key in conversation_keys:
                conv_group = f[conv_key]
                
                # 基本信息
                conversation_id = conv_group.attrs['conversation_ID']
                texts = [t.decode('utf-8') for t in conv_group['texts'][:]]
                speakers = [s.decode('utf-8') for s in conv_group['speakers'][:]]
                emotions = [e.decode('utf-8') for e in conv_group['emotions'][:]]
                
                # 多模态特征
                visual_features = conv_group['visual_features'][:]
                audio_features = conv_group['audio_features'][:]
                
                # 情感-原因对
                emotion_cause_pairs = []
                if 'emotion_cause_pairs' in conv_group:
                    emotion_cause_pairs = [p.decode('utf-8').split('|') 
                                         for p in conv_group['emotion_cause_pairs'][:]]
                
                # 构建话语对象
                utterances = []
                for i, (text, speaker, emotion) in enumerate(zip(texts, speakers, emotions)):
                    utt = UtteranceItem(i+1, text, emotion, speaker)
                    
                    # 添加情感-原因链接
                    for pair in emotion_cause_pairs:
                        if len(pair) >= 2:
                            emotion_part, cause_part = pair[0], pair[1]
                            
                            # 解析情感话语ID和情感类型
                            emotion_parts = emotion_part.split("_")
                            if len(emotion_parts) >= 2:
                                emotion_utt_id = int(emotion_parts[0])
                                emotion_type = "_".join(emotion_parts[1:])
                                
                                # 解析原因话语ID
                                cause_parts = cause_part.split("_")
                                if len(cause_parts) >= 1 and cause_parts[0].isdigit():
                                    cause_utt_id = int(cause_parts[0])
                                    
                                    # 如果当前话语是情感话语
                                    if i+1 == emotion_utt_id and emotion == emotion_type:
                                        link = EmotionCauseLink(emotion_utt_id, cause_utt_id, emotion_type)
                                        utt.append_emotion_cause_link(link)
                    
                    utterances.append(utt)
                
                conversations.append({
                    'conversation_id': conversation_id,
                    'utterances': utterances,
                    'visual_features': visual_features,
                    'audio_features': audio_features
                })
                
        return conversations
    
    def __len__(self) -> int:
        return len(self.conversations)
    
    def __getitem__(self, idx: int) -> Tuple:
        """获取单个对话数据"""
        conv_data = self.conversations[idx]
        utterances = conv_data['utterances']
        visual_features = conv_data['visual_features']
        audio_features = conv_data['audio_features']

        # 准备对话数据
        dialog_data = self._prepare_dialog(utterances, visual_features, audio_features)

        return dialog_data

    def _prepare_dialog(self, utterances: List[UtteranceItem],
                       visual_features: np.ndarray,
                       audio_features: np.ndarray) -> Tuple:
        """
        准备单个对话的数据

        Args:
            utterances: 话语列表
            visual_features: 视觉特征 [n_utterances, visual_dim]
            audio_features: 音频特征 [n_utterances, audio_dim]

        Returns:
            对话数据元组
        """
        # 提取基本信息
        texts = [utt.utterance_text for utt in utterances]
        emotions = []
        speakers = []
        links = []

        for utt in utterances:
            # 情感标签
            if utt.utterance_emotion not in self.em2i:
                emotions.append(0)
            else:
                emotions.append(self.em2i[utt.utterance_emotion])

            # 说话者标签
            if utt.utterance_speaker not in self.sp2i:
                speakers.append(0)
            else:
                speakers.append(self.sp2i[utt.utterance_speaker])

            # 情感-原因链接
            for link in utt.emotion_cause_links:
                srcid = int(link.source_id) - 1
                trgid = int(link.target_id) - 1
                links.append([srcid, trgid])

        # 文本分词
        tokens = self.tokenizer(texts, add_special_tokens=False)

        # 处理多模态特征
        processed_visual = self._process_visual_features(visual_features)
        processed_audio = self._process_audio_features(audio_features)

        # 构建图结构
        dialog_length = len(utterances)
        edge_index_list, edge_type_list, edge_norm_list, emotion_cause_edge_indices = \
            self._build_multimodal_graph(dialog_length, speakers, emotions, tokens, links)

        # 处理token序列
        dialog_tokens = [self.CLS]
        dialog_uttid = [0]

        for i, t in enumerate(tokens['input_ids']):
            t_with_sep = t + [self.SEP]
            dialog_tokens.extend(t_with_sep)
            dialog_uttid.extend([i+1] * len(t_with_sep))

        # 截断或填充到指定长度
        if len(dialog_tokens) > 512:
            dialog_tokens = dialog_tokens[:512]
            dialog_uttid = dialog_uttid[:512]

        # 创建mask
        dialog_mask = [1] * len(dialog_tokens)

        # 填充到固定长度
        while len(dialog_tokens) < 512:
            dialog_tokens.append(self.PAD)
            dialog_uttid.append(0)
            dialog_mask.append(0)

        # 话语级别的mask
        utt_mask = [1] * dialog_length
        while len(utt_mask) < 50:  # 假设最大话语数为50
            utt_mask.append(0)

        # 说话者和情感填充
        while len(speakers) < 50:
            speakers.append(0)
        while len(emotions) < 50:
            emotions.append(0)

        return (
            torch.tensor(dialog_tokens, dtype=torch.long),
            torch.tensor(dialog_uttid, dtype=torch.long),
            torch.tensor(dialog_mask, dtype=torch.long),
            torch.tensor(utt_mask[:50], dtype=torch.long),
            torch.tensor(speakers[:50], dtype=torch.long),
            torch.tensor(emotions[:50], dtype=torch.long),
            edge_index_list,
            edge_type_list,
            edge_norm_list,
            emotion_cause_edge_indices,
            processed_visual,  # 新增：视觉特征
            processed_audio    # 新增：音频特征
        )

    def _process_visual_features(self, visual_features: np.ndarray) -> torch.Tensor:
        """处理视觉特征"""
        # 转换为tensor
        visual_tensor = torch.tensor(visual_features, dtype=torch.float32)

        # 归一化
        if self.normalize_features:
            # 检查是否有有效特征（非零向量）
            valid_mask = torch.norm(visual_tensor, dim=1) > 1e-6
            if valid_mask.any():
                # 只对有效特征进行归一化
                visual_tensor[valid_mask] = torch.nn.functional.normalize(
                    visual_tensor[valid_mask], p=2, dim=1
                )

        return visual_tensor

    def _process_audio_features(self, audio_features: np.ndarray) -> torch.Tensor:
        """处理音频特征"""
        # 转换为tensor
        audio_tensor = torch.tensor(audio_features, dtype=torch.float32)

        # 归一化
        if self.normalize_features:
            # 检查是否有有效特征（非零向量）
            valid_mask = torch.norm(audio_tensor, dim=1) > 1e-6
            if valid_mask.any():
                # 只对有效特征进行归一化
                audio_tensor[valid_mask] = torch.nn.functional.normalize(
                    audio_tensor[valid_mask], p=2, dim=1
                )

        return audio_tensor

    def _build_multimodal_graph(self, dialog_length: int, speakers: List[int],
                               emotions: List[int], tokens: Dict,
                               links: List[List[int]]) -> Tuple[List, List, List, List]:
        """
        构建多模态图结构
        借鉴MMGCN的图构建策略，支持模态内和模态间连接

        Args:
            dialog_length: 对话长度
            speakers: 说话者列表
            emotions: 情感列表
            tokens: 分词结果
            links: 情感-原因链接

        Returns:
            edge_index_list, edge_type_list, edge_norm_list, emotion_cause_edge_indices
        """
        edge_index_list = []
        edge_type_list = []
        edge_norm_list = []
        emotion_cause_edge_indices = []

        # 1. 添加情感-原因对边
        for i, (src, tgt) in enumerate(links):
            if 0 <= src < dialog_length and 0 <= tgt < dialog_length:
                edge_index_list.append([src, tgt])
                edge_type_list.append(EDGE_TYPE_EMOTION_CAUSE)
                edge_norm_list.append(1.0)
                emotion_cause_edge_indices.append(len(edge_index_list) - 1)

        # 2. 添加时序边
        self._add_temporal_edges(dialog_length, speakers, edge_index_list, edge_type_list, edge_norm_list)

        # 3. 添加说话者关系边
        self._add_speaker_edges(dialog_length, speakers, edge_index_list, edge_type_list, edge_norm_list)

        # 4. 添加情感语义边
        self._add_emotion_semantic_edges(dialog_length, emotions, edge_index_list, edge_type_list, edge_norm_list)

        return edge_index_list, edge_type_list, edge_norm_list, emotion_cause_edge_indices

    def _add_temporal_edges(self, dialog_length: int, speakers: List[int],
                           edge_index_list: List, edge_type_list: List, edge_norm_list: List):
        """添加时序边"""
        for i in range(dialog_length):
            # 前向时序边
            for j in range(i + 1, min(i + self.window_future + 1, dialog_length)):
                edge_index_list.append([i, j])
                edge_type_list.append(EDGE_TYPE_TEMPORAL_FORWARD)
                # 距离越近权重越大
                weight = 0.9 / (1.0 + 0.1 * (j - i))
                edge_norm_list.append(weight)

            # 后向时序边
            for j in range(max(0, i - self.window_past), i):
                edge_index_list.append([i, j])
                edge_type_list.append(EDGE_TYPE_TEMPORAL_BACKWARD)
                # 距离越近权重越大
                weight = 0.9 / (1.0 + 0.1 * (i - j))
                edge_norm_list.append(weight)

    def _add_speaker_edges(self, dialog_length: int, speakers: List[int],
                          edge_index_list: List, edge_type_list: List, edge_norm_list: List):
        """添加说话者关系边"""
        for i in range(dialog_length):
            for j in range(i + 1, dialog_length):
                if speakers[i] == speakers[j] and speakers[i] != 0:
                    # 同说话者边
                    edge_index_list.append([i, j])
                    edge_type_list.append(EDGE_TYPE_SAME_SPEAKER)
                    edge_norm_list.append(0.8)

                    edge_index_list.append([j, i])
                    edge_type_list.append(EDGE_TYPE_SAME_SPEAKER)
                    edge_norm_list.append(0.8)
                elif abs(i - j) == 1:
                    # 相邻不同说话者边
                    edge_index_list.append([i, j])
                    edge_type_list.append(EDGE_TYPE_DIFF_SPEAKER)
                    edge_norm_list.append(0.6)

    def _add_emotion_semantic_edges(self, dialog_length: int, emotions: List[int],
                                   edge_index_list: List, edge_type_list: List, edge_norm_list: List):
        """添加情感语义边"""
        for i in range(dialog_length):
            for j in range(i + 1, dialog_length):
                emotion_i = emotions[i]
                emotion_j = emotions[j]

                # 跳过无效情感
                if emotion_i == 0 or emotion_j == 0:
                    continue

                # 相同情感边
                if emotion_i == emotion_j:
                    edge_index_list.append([i, j])
                    edge_type_list.append(EDGE_TYPE_SAME_EMOTION)
                    # 根据距离设置权重
                    distance = j - i
                    weight = 0.7 / (1.0 + 0.05 * distance)
                    edge_norm_list.append(weight)

                    edge_index_list.append([j, i])
                    edge_type_list.append(EDGE_TYPE_SAME_EMOTION)
                    edge_norm_list.append(weight)


def multimodal_collate_fn(batch):
    """
    多模态数据的批处理函数
    """
    # 分离不同类型的数据
    dialog_tokens = torch.stack([item[0] for item in batch])
    dialog_uttid = torch.stack([item[1] for item in batch])
    dialog_mask = torch.stack([item[2] for item in batch])
    utt_mask = torch.stack([item[3] for item in batch])
    utt_speakers = torch.stack([item[4] for item in batch])
    utt_emotions = torch.stack([item[5] for item in batch])

    # 边数据（列表形式）
    edge_index_batch = [item[6] for item in batch]
    edge_type_batch = [item[7] for item in batch]
    edge_norm_batch = [item[8] for item in batch]
    emotion_cause_edge_indices_batch = [item[9] for item in batch]

    # 多模态特征
    visual_features_batch = [item[10] for item in batch]
    audio_features_batch = [item[11] for item in batch]

    return (
        dialog_tokens, dialog_uttid, dialog_mask, utt_mask, utt_speakers, utt_emotions,
        edge_index_batch, edge_type_batch, edge_norm_batch, emotion_cause_edge_indices_batch,
        visual_features_batch, audio_features_batch
    )
