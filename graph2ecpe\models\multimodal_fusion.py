"""
多模态特征融合模块
借鉴MMGCN的多模态处理策略，实现文本、视觉、音频特征的融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Optional, Dict


class MultimodalFeatureFusion(nn.Module):
    """
    多模态特征融合模块
    借鉴MMGCN的策略，支持文本、视觉、音频三种模态的融合
    """
    
    def __init__(self,
                 text_dim: int = 768,
                 visual_dim: int = 4096,
                 audio_dim: int = 6373,
                 hidden_dim: int = 256,
                 n_speakers: int = 10,
                 modalities: List[str] = ['text', 'visual', 'audio'],
                 use_speaker_embedding: bool = True,
                 use_modal_embedding: bool = True,
                 dropout: float = 0.2):
        """
        初始化多模态融合模块
        
        Args:
            text_dim: 文本特征维度
            visual_dim: 视觉特征维度
            audio_dim: 音频特征维度
            hidden_dim: 隐藏层维度
            n_speakers: 说话者数量
            modalities: 使用的模态列表
            use_speaker_embedding: 是否使用说话者嵌入
            use_modal_embedding: 是否使用模态嵌入
            dropout: dropout率
        """
        super().__init__()
        
        self.modalities = modalities
        self.hidden_dim = hidden_dim
        self.use_speaker_embedding = use_speaker_embedding
        self.use_modal_embedding = use_modal_embedding
        
        # 模态特征投影层
        if 'text' in modalities:
            self.text_fc = nn.Linear(text_dim, hidden_dim)
        if 'visual' in modalities:
            self.visual_fc = nn.Linear(visual_dim, hidden_dim)
        if 'audio' in modalities:
            self.audio_fc = nn.Linear(audio_dim, hidden_dim)
        
        # 说话者嵌入
        if use_speaker_embedding:
            self.speaker_embeddings = nn.Embedding(n_speakers, hidden_dim)
            # 为每个模态创建独立的说话者嵌入
            if 'text' in modalities:
                self.text_spk_embs = nn.Embedding(n_speakers, hidden_dim)
            if 'visual' in modalities:
                self.visual_spk_embs = nn.Embedding(n_speakers, hidden_dim)
            if 'audio' in modalities:
                self.audio_spk_embs = nn.Embedding(n_speakers, hidden_dim)
        
        # 模态嵌入
        if use_modal_embedding:
            self.modal_embeddings = nn.Embedding(len(modalities), hidden_dim)
        
        # 特征融合层
        fusion_input_dim = hidden_dim * len(modalities)
        self.fusion_fc = nn.Linear(fusion_input_dim, hidden_dim)
        
        # 激活函数和dropout
        self.act_fn = nn.ReLU()
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, mean=0, std=0.1)
    
    def forward(self, 
                text_features: torch.Tensor,
                visual_features: List[torch.Tensor],
                audio_features: List[torch.Tensor],
                speaker_ids: torch.Tensor,
                utterance_lengths: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            text_features: 文本特征 [batch_size, max_utts, text_dim]
            visual_features: 视觉特征列表，每个元素为 [n_utts, visual_dim]
            audio_features: 音频特征列表，每个元素为 [n_utts, audio_dim]
            speaker_ids: 说话者ID [batch_size, max_utts]
            utterance_lengths: 每个对话的实际话语数量 [batch_size]
            
        Returns:
            fused_features: 融合后的特征 [total_utts, hidden_dim]
            batch_indices: 每个话语所属的batch索引 [total_utts]
        """
        device = text_features.device
        batch_size = text_features.size(0)
        
        # 收集所有模态的特征
        all_modal_features = []
        all_batch_indices = []
        
        # 处理每个batch
        for b in range(batch_size):
            cur_len = min(utterance_lengths[b].item(), text_features.size(1))
            if cur_len <= 0:
                continue
                
            modal_features = []
            
            # 处理文本特征
            if 'text' in self.modalities:
                text_feat = text_features[b, :cur_len]  # [cur_len, text_dim]
                text_feat = self.text_fc(text_feat)  # [cur_len, hidden_dim]

                # 添加说话者嵌入
                if self.use_speaker_embedding:
                    spk_ids = speaker_ids[b, :cur_len]
                    # 确保说话者ID在有效范围内
                    spk_ids = torch.clamp(spk_ids, 0, self.text_spk_embs.num_embeddings - 1)
                    text_spk_emb = self.text_spk_embs(spk_ids)
                    text_feat = text_feat + text_spk_emb

                modal_features.append(text_feat)
            
            # 处理视觉特征
            if 'visual' in self.modalities and b < len(visual_features):
                visual_feat = visual_features[b][:cur_len]  # [cur_len, visual_dim]
                visual_feat = self.visual_fc(visual_feat)  # [cur_len, hidden_dim]

                # 添加说话者嵌入
                if self.use_speaker_embedding:
                    spk_ids = speaker_ids[b, :cur_len]
                    # 确保说话者ID在有效范围内
                    spk_ids = torch.clamp(spk_ids, 0, self.visual_spk_embs.num_embeddings - 1)
                    visual_spk_emb = self.visual_spk_embs(spk_ids)
                    visual_feat = visual_feat + visual_spk_emb

                modal_features.append(visual_feat)
            
            # 处理音频特征
            if 'audio' in self.modalities and b < len(audio_features):
                audio_feat = audio_features[b][:cur_len]  # [cur_len, audio_dim]
                audio_feat = self.audio_fc(audio_feat)  # [cur_len, hidden_dim]

                # 添加说话者嵌入
                if self.use_speaker_embedding:
                    spk_ids = speaker_ids[b, :cur_len]
                    # 确保说话者ID在有效范围内
                    spk_ids = torch.clamp(spk_ids, 0, self.audio_spk_embs.num_embeddings - 1)
                    audio_spk_emb = self.audio_spk_embs(spk_ids)
                    audio_feat = audio_feat + audio_spk_emb

                modal_features.append(audio_feat)
            
            # 添加模态嵌入
            if self.use_modal_embedding:
                modal_indices = torch.arange(len(modal_features), device=device)
                modal_embs = self.modal_embeddings(modal_indices)  # [n_modals, hidden_dim]
                
                for i, feat in enumerate(modal_features):
                    modal_features[i] = feat + modal_embs[i].unsqueeze(0).expand_as(feat)
            
            # 融合模态特征
            if len(modal_features) > 1:
                # 拼接所有模态特征
                concatenated = torch.cat(modal_features, dim=-1)  # [cur_len, hidden_dim * n_modals]
                fused = self.fusion_fc(concatenated)  # [cur_len, hidden_dim]
                fused = self.act_fn(fused)
                fused = self.dropout(fused)
            else:
                fused = modal_features[0]
            
            all_modal_features.append(fused)
            all_batch_indices.append(torch.full((cur_len,), b, dtype=torch.long, device=device))
        
        # 合并所有batch的特征
        if all_modal_features:
            fused_features = torch.cat(all_modal_features, dim=0)
            batch_indices = torch.cat(all_batch_indices, dim=0)
        else:
            # 创建默认值
            fused_features = torch.zeros((1, self.hidden_dim), device=device)
            batch_indices = torch.zeros(1, dtype=torch.long, device=device)
        
        return fused_features, batch_indices


class MultimodalGraphBuilder:
    """
    多模态图构建器
    借鉴MMGCN的图构建策略，支持模态内和模态间的连接
    """
    
    @staticmethod
    def create_multimodal_adjacency(features_list: List[torch.Tensor],
                                   utterance_lengths: List[int],
                                   modalities: List[str] = ['text', 'visual', 'audio'],
                                   connection_strategy: str = 'cosine') -> torch.Tensor:
        """
        创建多模态邻接矩阵
        
        Args:
            features_list: 特征列表，每个元素对应一个模态的特征
            utterance_lengths: 每个对话的话语数量
            modalities: 模态列表
            connection_strategy: 连接策略 ('cosine', 'attention', 'learned')
            
        Returns:
            adj_matrix: 多模态邻接矩阵
        """
        device = features_list[0].device
        n_modals = len(features_list)
        total_length = sum(utterance_lengths)
        
        # 创建大的邻接矩阵
        adj_matrix = torch.zeros((n_modals * total_length, n_modals * total_length), device=device)
        
        start_idx = 0
        for dia_len in utterance_lengths:
            # 为每个对话构建子邻接矩阵
            sub_adjs = []
            
            # 计算模态内相似度
            for modal_idx, features in enumerate(features_list):
                sub_adj = torch.zeros((dia_len, dia_len), device=device)
                
                if connection_strategy == 'cosine':
                    # 优化：使用批量计算余弦相似度
                    modal_feat = features[start_idx:start_idx + dia_len]
                    # 归一化
                    norm_feat = F.normalize(modal_feat, p=2, dim=1)
                    # 批量计算余弦相似度矩阵 - 优化的实现
                    cos_sim = torch.mm(norm_feat, norm_feat.t())
                    # 数值稳定性处理
                    cos_sim = torch.clamp(cos_sim, -0.99999, 0.99999)
                    sim_matrix = 1 - torch.acos(cos_sim) / np.pi
                    sub_adj = sim_matrix
                
                sub_adjs.append(sub_adj)
            
            # 填充邻接矩阵
            for m in range(n_modals):
                for n in range(n_modals):
                    m_start = start_idx + total_length * m
                    n_start = start_idx + total_length * n
                    
                    if m == n:
                        # 模态内连接
                        adj_matrix[m_start:m_start + dia_len, n_start:n_start + dia_len] = sub_adjs[m]
                    else:
                        # 优化：批量计算模态间连接
                        feat_m = features_list[m][start_idx:start_idx + dia_len]
                        feat_n = features_list[n][start_idx:start_idx + dia_len]

                        # 批量计算余弦相似度（对角线元素）
                        norm_feat_m = F.normalize(feat_m, p=2, dim=1)
                        norm_feat_n = F.normalize(feat_n, p=2, dim=1)

                        # 逐元素相似度计算（对角线连接）
                        cos_sim = torch.sum(norm_feat_m * norm_feat_n, dim=1)
                        cos_sim = torch.clamp(cos_sim, -0.99999, 0.99999)
                        sim_weights = 1 - torch.acos(cos_sim) / np.pi

                        # 填充对角线连接
                        for i in range(dia_len):
                            adj_matrix[m_start + i, n_start + i] = sim_weights[i]
                            adj_matrix[n_start + i, m_start + i] = sim_weights[i]
            
            start_idx += dia_len
        
        # 归一化邻接矩阵
        d = adj_matrix.sum(1)
        d_sqrt_inv = torch.pow(d + 1e-8, -0.5)
        d_sqrt_inv[torch.isinf(d_sqrt_inv)] = 0
        D = torch.diag(d_sqrt_inv)
        adj_matrix = torch.mm(torch.mm(D, adj_matrix), D)
        
        return adj_matrix
