#!/usr/bin/env python3
"""
增强的多模态图构建器
基于MMGCN的复杂图构建策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import List, Optional, Tuple


class EnhancedMultimodalGraphBuilder:
    """
    增强的多模态图构建器
    基于MMGCN的复杂图构建策略
    """
    
    def __init__(self, 
                 connection_strategy: str = 'enhanced_cosine',
                 cross_modal_strategy: str = 'single',
                 window_size: int = 10,
                 similarity_threshold: float = 0.1,
                 normalize_adj: bool = True):
        """
        Args:
            connection_strategy: 连接策略 ('enhanced_cosine', 'attention', 'learned')
            cross_modal_strategy: 跨模态连接策略 ('single', 'window', 'fc', 'speaker_aware')
            window_size: 窗口大小（用于window策略）
            similarity_threshold: 相似度阈值
            normalize_adj: 是否标准化邻接矩阵
        """
        self.connection_strategy = connection_strategy
        self.cross_modal_strategy = cross_modal_strategy
        self.window_size = window_size
        self.similarity_threshold = similarity_threshold
        self.normalize_adj = normalize_adj
        
        # 余弦相似度函数
        self.cosine_sim = torch.nn.CosineSimilarity(dim=-1)
    
    def create_multimodal_adjacency(self, 
                                   features_list: List[torch.Tensor],
                                   utterance_lengths: List[int],
                                   speaker_ids: Optional[torch.Tensor] = None,
                                   modalities: List[str] = ['text', 'visual', 'audio']) -> torch.Tensor:
        """
        创建增强的多模态邻接矩阵
        
        Args:
            features_list: 特征列表，每个元素对应一个模态的特征
            utterance_lengths: 每个对话的话语数量
            speaker_ids: 说话者ID [total_utterances]
            modalities: 模态列表
            
        Returns:
            adj_matrix: 增强的多模态邻接矩阵
        """
        device = features_list[0].device
        n_modals = len(features_list)
        total_length = sum(utterance_lengths)
        
        # 创建大的邻接矩阵 [n_modals * total_length, n_modals * total_length]
        adj_matrix = torch.zeros((n_modals * total_length, n_modals * total_length), device=device)
        
        # 1. 构建模态内连接
        self._build_intra_modal_connections(adj_matrix, features_list, utterance_lengths, n_modals, total_length)
        
        # 2. 构建模态间连接
        self._build_inter_modal_connections(adj_matrix, features_list, utterance_lengths, speaker_ids, n_modals, total_length)
        
        # 3. 标准化邻接矩阵
        if self.normalize_adj:
            adj_matrix = self._normalize_adjacency(adj_matrix)
        
        return adj_matrix
    
    def _build_intra_modal_connections(self,
                                      adj_matrix: torch.Tensor,
                                      features_list: List[torch.Tensor],
                                      utterance_lengths: List[int],
                                      n_modals: int,
                                      total_length: int):
        """构建模态内连接"""
        # 为每个模态分别构建连接
        for modal_idx, features in enumerate(features_list):
            modal_offset = modal_idx * total_length
            start = 0

            for dialog_idx, dialog_len in enumerate(utterance_lengths):
                # 获取当前对话在当前模态的特征
                dialog_features = features[start:start + dialog_len]

                # 计算模态内相似度矩阵
                if dialog_len > 0:
                    intra_modal_adj = self._compute_similarity_matrix(dialog_features)

                    # 确保相似度矩阵大小正确
                    if intra_modal_adj.size(0) == dialog_len and intra_modal_adj.size(1) == dialog_len:
                        # 填充到大邻接矩阵中
                        dialog_start = modal_offset + start
                        dialog_end = modal_offset + start + dialog_len
                        adj_matrix[dialog_start:dialog_end, dialog_start:dialog_end] = intra_modal_adj

                start += dialog_len
    
    def _build_inter_modal_connections(self, 
                                      adj_matrix: torch.Tensor,
                                      features_list: List[torch.Tensor],
                                      utterance_lengths: List[int],
                                      speaker_ids: Optional[torch.Tensor],
                                      n_modals: int,
                                      total_length: int):
        """构建模态间连接"""
        if self.cross_modal_strategy == 'single':
            self._build_single_cross_modal(adj_matrix, features_list, utterance_lengths, n_modals, total_length)
        elif self.cross_modal_strategy == 'window':
            self._build_window_cross_modal(adj_matrix, features_list, utterance_lengths, n_modals, total_length)
        elif self.cross_modal_strategy == 'fc':
            self._build_fc_cross_modal(adj_matrix, features_list, utterance_lengths, n_modals, total_length)
        elif self.cross_modal_strategy == 'speaker_aware':
            self._build_speaker_aware_cross_modal(adj_matrix, features_list, utterance_lengths, speaker_ids, n_modals, total_length)
    
    def _build_single_cross_modal(self, 
                                 adj_matrix: torch.Tensor,
                                 features_list: List[torch.Tensor],
                                 utterance_lengths: List[int],
                                 n_modals: int,
                                 total_length: int):
        """单点跨模态连接：同一话语的不同模态直接连接"""
        for i in range(total_length):
            for m in range(n_modals):
                for n in range(m + 1, n_modals):
                    # 计算跨模态相似度
                    feat_m = features_list[m][i]
                    feat_n = features_list[n][i]
                    
                    similarity = self._compute_cross_modal_similarity(feat_m, feat_n)
                    
                    # 双向连接
                    idx_m = i + total_length * m
                    idx_n = i + total_length * n
                    
                    adj_matrix[idx_m, idx_n] = similarity
                    adj_matrix[idx_n, idx_m] = similarity
    
    def _build_window_cross_modal(self, 
                                 adj_matrix: torch.Tensor,
                                 features_list: List[torch.Tensor],
                                 utterance_lengths: List[int],
                                 n_modals: int,
                                 total_length: int):
        """窗口跨模态连接：在窗口范围内连接不同模态"""
        start = 0
        
        for dialog_len in utterance_lengths:
            for i in range(dialog_len):
                for m in range(n_modals):
                    for n in range(m + 1, n_modals):
                        # 定义窗口范围
                        left = max(i - self.window_size, 0)
                        right = min(i + self.window_size + 1, dialog_len)
                        
                        for j in range(left, right):
                            feat_m = features_list[m][start + i]
                            feat_n = features_list[n][start + j]
                            
                            similarity = self._compute_cross_modal_similarity(feat_m, feat_n)
                            
                            # 双向连接
                            idx_m = (start + i) + total_length * m
                            idx_n = (start + j) + total_length * n
                            
                            adj_matrix[idx_m, idx_n] = similarity
                            adj_matrix[idx_n, idx_m] = similarity
            
            start += dialog_len
    
    def _build_fc_cross_modal(self, 
                             adj_matrix: torch.Tensor,
                             features_list: List[torch.Tensor],
                             utterance_lengths: List[int],
                             n_modals: int,
                             total_length: int):
        """全连接跨模态连接：每个对话内的所有模态全连接"""
        start = 0
        
        for dialog_len in utterance_lengths:
            for i in range(dialog_len):
                for j in range(dialog_len):
                    for m in range(n_modals):
                        for n in range(m + 1, n_modals):
                            feat_m = features_list[m][start + i]
                            feat_n = features_list[n][start + j]
                            
                            similarity = self._compute_cross_modal_similarity(feat_m, feat_n)
                            
                            # 双向连接
                            idx_m = (start + i) + total_length * m
                            idx_n = (start + j) + total_length * n
                            
                            adj_matrix[idx_m, idx_n] = similarity
                            adj_matrix[idx_n, idx_m] = similarity
            
            start += dialog_len
    
    def _build_speaker_aware_cross_modal(self, 
                                        adj_matrix: torch.Tensor,
                                        features_list: List[torch.Tensor],
                                        utterance_lengths: List[int],
                                        speaker_ids: Optional[torch.Tensor],
                                        n_modals: int,
                                        total_length: int):
        """说话者感知的跨模态连接"""
        if speaker_ids is None:
            # 如果没有说话者信息，回退到单点连接
            self._build_single_cross_modal(adj_matrix, features_list, utterance_lengths, n_modals, total_length)
            return
        
        start = 0
        
        for dialog_len in utterance_lengths:
            dialog_speakers = speaker_ids[start:start + dialog_len]
            
            for i in range(dialog_len):
                for j in range(dialog_len):
                    # 同一说话者的话语有更强的连接
                    speaker_weight = 1.0 if dialog_speakers[i] == dialog_speakers[j] else 0.5
                    
                    for m in range(n_modals):
                        for n in range(m + 1, n_modals):
                            feat_m = features_list[m][start + i]
                            feat_n = features_list[n][start + j]
                            
                            similarity = self._compute_cross_modal_similarity(feat_m, feat_n) * speaker_weight
                            
                            # 双向连接
                            idx_m = (start + i) + total_length * m
                            idx_n = (start + j) + total_length * n
                            
                            adj_matrix[idx_m, idx_n] = similarity
                            adj_matrix[idx_n, idx_m] = similarity
            
            start += dialog_len

    def _compute_similarity_matrix(self, features: torch.Tensor) -> torch.Tensor:
        """计算特征矩阵的相似度矩阵"""
        if self.connection_strategy == 'enhanced_cosine':
            return self._enhanced_cosine_similarity(features)
        elif self.connection_strategy == 'attention':
            return self._attention_similarity(features)
        elif self.connection_strategy == 'learned':
            return self._learned_similarity(features)
        else:
            # 默认使用增强余弦相似度
            return self._enhanced_cosine_similarity(features)

    def _enhanced_cosine_similarity(self, features: torch.Tensor) -> torch.Tensor:
        """增强的余弦相似度计算"""
        # 确保features是2D张量
        if features.dim() > 2:
            # 如果是3D或更高维，重塑为2D
            original_shape = features.shape
            features = features.view(-1, original_shape[-1])

        # 标准化特征
        features_norm = F.normalize(features, p=2, dim=-1)

        # 计算余弦相似度矩阵
        similarity_matrix = torch.mm(features_norm, features_norm.t())

        # 应用阈值过滤
        similarity_matrix = torch.where(
            similarity_matrix > self.similarity_threshold,
            similarity_matrix,
            torch.zeros_like(similarity_matrix)
        )

        # 添加自连接
        similarity_matrix.fill_diagonal_(1.0)

        return similarity_matrix

    def _attention_similarity(self, features: torch.Tensor) -> torch.Tensor:
        """基于注意力的相似度计算"""
        # 确保features是2D张量
        if features.dim() > 2:
            original_shape = features.shape
            features = features.view(-1, original_shape[-1])

        seq_len, feat_dim = features.shape

        # 简化的注意力机制
        query = features.unsqueeze(1)  # [seq_len, 1, feat_dim]
        key = features.unsqueeze(0)    # [1, seq_len, feat_dim]

        # 计算注意力分数
        attention_scores = torch.sum(query * key, dim=-1) / math.sqrt(feat_dim)
        attention_weights = torch.softmax(attention_scores, dim=-1)

        # 应用阈值
        attention_weights = torch.where(
            attention_weights > self.similarity_threshold,
            attention_weights,
            torch.zeros_like(attention_weights)
        )

        return attention_weights

    def _learned_similarity(self, features: torch.Tensor) -> torch.Tensor:
        """学习的相似度计算（简化版本）"""
        # 这里可以实现更复杂的学习相似度，暂时使用余弦相似度
        return self._enhanced_cosine_similarity(features)

    def _compute_cross_modal_similarity(self, feat1: torch.Tensor, feat2: torch.Tensor) -> torch.Tensor:
        """计算跨模态相似度"""
        if self.connection_strategy == 'enhanced_cosine':
            # 标准化特征
            feat1_norm = F.normalize(feat1.unsqueeze(0), p=2, dim=-1)
            feat2_norm = F.normalize(feat2.unsqueeze(0), p=2, dim=-1)

            # 计算余弦相似度
            similarity = torch.sum(feat1_norm * feat2_norm, dim=-1)

            # 确保similarity是标量
            if similarity.numel() > 1:
                similarity = similarity.mean()

            # 应用阈值
            similarity_value = similarity.item()
            return similarity_value if similarity_value > self.similarity_threshold else 0.0

        elif self.connection_strategy == 'attention':
            # 简化的跨模态注意力
            similarity = torch.sum(feat1 * feat2) / math.sqrt(feat1.size(-1))
            similarity = torch.sigmoid(similarity)

            # 确保similarity是标量
            if similarity.numel() > 1:
                similarity = similarity.mean()

            return similarity.item() if similarity.item() > self.similarity_threshold else 0.0

        else:
            # 默认使用余弦相似度
            feat1_norm = F.normalize(feat1.unsqueeze(0), p=2, dim=-1)
            feat2_norm = F.normalize(feat2.unsqueeze(0), p=2, dim=-1)
            similarity = torch.sum(feat1_norm * feat2_norm, dim=-1)

            # 确保similarity是标量
            if similarity.numel() > 1:
                similarity = similarity.mean()

            return similarity.item() if similarity.item() > self.similarity_threshold else 0.0

    def _normalize_adjacency(self, adj_matrix: torch.Tensor) -> torch.Tensor:
        """标准化邻接矩阵"""
        # 计算度矩阵
        degree = adj_matrix.sum(dim=1)

        # 避免除零
        degree = torch.where(degree > 0, degree, torch.ones_like(degree))

        # 计算度矩阵的-0.5次幂
        degree_inv_sqrt = torch.pow(degree, -0.5)
        degree_matrix = torch.diag(degree_inv_sqrt)

        # 对称标准化: D^(-1/2) * A * D^(-1/2)
        normalized_adj = torch.mm(torch.mm(degree_matrix, adj_matrix), degree_matrix)

        return normalized_adj

    def create_edge_index_from_adjacency(self, adj_matrix: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """从邻接矩阵创建边索引和边权重"""
        # 找到非零元素的索引
        edge_indices = torch.nonzero(adj_matrix, as_tuple=False).t()
        edge_weights = adj_matrix[edge_indices[0], edge_indices[1]]

        return edge_indices, edge_weights


class MultimodalEdgeTypeEncoder:
    """
    多模态边类型编码器
    为不同类型的连接分配不同的边类型
    """

    def __init__(self, n_modals: int = 3):
        self.n_modals = n_modals

        # 定义边类型
        self.INTRA_MODAL_EDGE = 0      # 模态内连接
        self.INTER_MODAL_EDGE = 1      # 模态间连接
        self.SPEAKER_EDGE = 2          # 说话者连接
        self.TEMPORAL_EDGE = 3         # 时序连接
        self.SEMANTIC_EDGE = 4         # 语义连接

        self.edge_type_names = {
            0: "intra_modal",
            1: "inter_modal",
            2: "speaker",
            3: "temporal",
            4: "semantic"
        }

    def encode_edge_types(self,
                         adj_matrix: torch.Tensor,
                         features_list: List[torch.Tensor],
                         utterance_lengths: List[int],
                         speaker_ids: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        为邻接矩阵中的每条边分配类型

        Returns:
            edge_types: 边类型张量 [num_edges]
        """
        device = adj_matrix.device
        n_modals = len(features_list)
        total_length = sum(utterance_lengths)

        # 获取边索引
        edge_indices = torch.nonzero(adj_matrix, as_tuple=False)
        num_edges = edge_indices.size(0)

        # 初始化边类型
        edge_types = torch.zeros(num_edges, dtype=torch.long, device=device)

        for edge_idx, (i, j) in enumerate(edge_indices):
            # 确定节点所属的模态
            modal_i = i // total_length
            modal_j = j // total_length

            # 确定节点在模态内的位置
            pos_i = i % total_length
            pos_j = j % total_length

            if modal_i == modal_j:
                # 模态内连接
                if abs(pos_i - pos_j) <= 1:
                    edge_types[edge_idx] = self.TEMPORAL_EDGE  # 相邻时序连接
                else:
                    edge_types[edge_idx] = self.INTRA_MODAL_EDGE  # 一般模态内连接
            else:
                # 模态间连接
                if pos_i == pos_j:
                    edge_types[edge_idx] = self.INTER_MODAL_EDGE  # 同一话语的跨模态连接
                else:
                    edge_types[edge_idx] = self.SEMANTIC_EDGE  # 语义连接

        # 如果有说话者信息，进一步细化边类型
        if speaker_ids is not None:
            self._refine_edge_types_with_speaker(edge_types, edge_indices, speaker_ids, total_length)

        return edge_types

    def _refine_edge_types_with_speaker(self,
                                       edge_types: torch.Tensor,
                                       edge_indices: torch.Tensor,
                                       speaker_ids: torch.Tensor,
                                       total_length: int):
        """使用说话者信息细化边类型"""
        for edge_idx, (i, j) in enumerate(edge_indices):
            pos_i = i % total_length
            pos_j = j % total_length

            # 检查是否是同一说话者
            if pos_i < len(speaker_ids) and pos_j < len(speaker_ids):
                if speaker_ids[pos_i] == speaker_ids[pos_j]:
                    # 同一说话者的连接，提升为说话者边
                    if edge_types[edge_idx] == self.SEMANTIC_EDGE:
                        edge_types[edge_idx] = self.SPEAKER_EDGE
