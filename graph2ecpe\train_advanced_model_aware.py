#!/usr/bin/env python3
"""
高性能模型感知训练脚本
使用最先进的模型感知解码器，追求最佳性能
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import random
import logging
import os
from pathlib import Path

# 原始导入
from config import UnifiedConfig, get_config, set_config
from data import MultimodalDialogDataset, multimodal_collate_fn
from models import MultimodalGraph2SeqECPE, create_ecpe_vocab
from utils import prepare_target_sequences, compute_loss, compute_ecpe_metrics, UnifiedPostProcessor
from parser import parse_args_and_create_config, print_args

# 高性能模型感知解码器
from models.advanced_model_aware_decoder import AdvancedModelAwareDecoder

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def create_multimodal_datasets(config):
    """创建多模态数据集"""
    import os
    
    # 构建HDF5文件路径
    data_path = config.dataset.get_data_path()
    train_h5_path = os.path.join(data_path, f"{config.dataset.name}_train_multimodal.h5")
    dev_h5_path = os.path.join(data_path, f"{config.dataset.name}_dev_multimodal.h5")
    
    # 验证文件存在
    if not os.path.exists(train_h5_path):
        raise FileNotFoundError(f"训练数据文件不存在: {train_h5_path}")
    if not os.path.exists(dev_h5_path):
        raise FileNotFoundError(f"验证数据文件不存在: {dev_h5_path}")
    
    logger.info(f"加载训练数据: {train_h5_path}")
    logger.info(f"加载验证数据: {dev_h5_path}")
    
    train_dataset = MultimodalDialogDataset(
        h5_file_path=train_h5_path,
        tokenizer=config.model.plm_model,
        modalities=config.model.modalities,
        emotion_categories=config.dataset.get_emotion_categories(),
        speakers=config.dataset.get_speakers()
    )
    
    dev_dataset = MultimodalDialogDataset(
        h5_file_path=dev_h5_path,
        tokenizer=config.model.plm_model,
        modalities=config.model.modalities,
        emotion_categories=config.dataset.get_emotion_categories(),
        speakers=config.dataset.get_speakers()
    )
    
    return train_dataset, dev_dataset


class AdvancedTrainingStrategy:
    """高级训练策略"""
    
    def __init__(self, model, decoder, token2idx):
        self.model = model
        self.decoder = decoder
        self.token2idx = token2idx
        self.training_phase = 'warmup'  # warmup, mixed, advanced
        self.phase_epochs = {'warmup': 3, 'mixed': 5, 'advanced': 12}
    
    def get_training_mode(self, epoch):
        """获取当前训练模式"""
        if epoch <= self.phase_epochs['warmup']:
            return 'teacher_forcing'
        elif epoch <= self.phase_epochs['warmup'] + self.phase_epochs['mixed']:
            return 'mixed'
        else:
            return 'model_aware'
    
    def compute_loss(self, batch, target_seqs, epoch):
        """计算损失"""
        training_mode = self.get_training_mode(epoch)
        
        if training_mode == 'teacher_forcing':
            # 纯teacher forcing训练
            return self._teacher_forcing_loss(batch, target_seqs)
        elif training_mode == 'mixed':
            # 混合训练
            return self._mixed_training_loss(batch, target_seqs)
        else:
            # 高级模型感知训练
            return self._model_aware_loss(batch, target_seqs)
    
    def _teacher_forcing_loss(self, batch, target_seqs):
        """Teacher forcing损失"""
        outputs, _ = self.model(batch, target_seqs, teacher_forcing_ratio=1.0)
        criterion = nn.CrossEntropyLoss(ignore_index=self.token2idx.get('<pad>', 0))
        vocab_size = outputs.size(-1)
        
        # 确保形状匹配
        if outputs.size(1) != target_seqs.size(1):
            min_len = min(outputs.size(1), target_seqs.size(1))
            outputs = outputs[:, :min_len, :]
            target_seqs = target_seqs[:, :min_len]
        
        return criterion(outputs.view(-1, vocab_size), target_seqs.view(-1))
    
    def _mixed_training_loss(self, batch, target_seqs):
        """混合训练损失"""
        # 50%概率使用teacher forcing，50%使用模型感知
        if torch.rand(1).item() < 0.5:
            return self._teacher_forcing_loss(batch, target_seqs)
        else:
            return self._model_aware_loss(batch, target_seqs)
    
    def _model_aware_loss(self, batch, target_seqs):
        """模型感知损失"""
        try:
            # 使用高性能解码器生成预测
            predictions = self.decoder.advanced_decode(
                batch, use_constraints=True, beam_size=1, temperature=1.0
            )

            # 计算序列级别的损失
            sequence_loss = self._sequence_level_loss(predictions, target_seqs)

            # 结合teacher forcing损失以保持训练稳定性
            tf_loss = self._teacher_forcing_loss(batch, target_seqs)

            # 加权组合
            total_loss = 0.7 * tf_loss + 0.3 * sequence_loss

            return total_loss

        except Exception as e:
            logger.warning(f"模型感知损失计算失败，回退到teacher forcing: {e}")
            return self._teacher_forcing_loss(batch, target_seqs)
    
    def _sequence_level_loss(self, predictions, targets):
        """序列级别损失"""
        try:
            # 使用可微分的序列损失
            batch_size = min(predictions.size(0), targets.size(0))
            device = predictions.device

            # 计算序列长度差异损失
            pred_lengths = torch.sum(predictions != self.token2idx.get('<pad>', 0), dim=1).float()
            target_lengths = torch.sum(targets != self.token2idx.get('<pad>', 0), dim=1).float()
            length_loss = torch.mean(torch.abs(pred_lengths - target_lengths))

            # 计算token级别的重叠损失
            overlap_loss = 0.0
            for i in range(batch_size):
                pred_seq = predictions[i]
                target_seq = targets[i]

                # 计算token重叠
                min_len = min(pred_seq.size(0), target_seq.size(0))
                if min_len > 0:
                    pred_truncated = pred_seq[:min_len]
                    target_truncated = target_seq[:min_len]

                    # 计算匹配的token数量
                    matches = torch.sum(pred_truncated == target_truncated).float()
                    overlap_ratio = matches / min_len
                    overlap_loss += (1.0 - overlap_ratio)

            overlap_loss = overlap_loss / batch_size

            # 组合损失
            total_loss = length_loss + overlap_loss

            return total_loss

        except Exception as e:
            logger.warning(f"序列级别损失计算失败: {e}")
            device = predictions.device
            return torch.tensor(0.0, device=device, requires_grad=True)
    
    def _compute_edit_distance(self, seq1, seq2):
        """计算编辑距离"""
        m, n = len(seq1), len(seq2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if seq1[i-1] == seq2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
        
        return dp[m][n]


def main():
    """主函数 - 高性能模型感知训练"""
    # 解析参数和创建配置
    args = parse_args_and_create_config()
    config = get_config()
    config.update_from_args(args)

    # 启用多模态
    config.model.use_multimodal = True
    config.model.modalities = ['text', 'visual', 'audio']
    config.model.speakers = config.dataset.get_speakers()

    config.print_config()
    print_args(args)

    logger.info(f"🎯 开始高性能模型感知训练... (目标F1: 0.6)")

    set_seed(config.experiment.seed)
    device = torch.device(config.training.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 创建数据集
    train_dataset, dev_dataset = create_multimodal_datasets(config)

    emotion_categories = config.dataset.get_emotion_categories()
    token2idx, idx2token = create_ecpe_vocab(emotion_categories, max_utterances=100)
    config.model.vocab_size = len(token2idx)

    logger.info(f"词汇表大小: {config.model.vocab_size}")

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.training.batch_size,
        shuffle=True,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    dev_loader = DataLoader(
        dev_dataset,
        batch_size=config.training.batch_size,
        shuffle=False,
        collate_fn=multimodal_collate_fn,
        num_workers=config.training.dataloader_num_workers
    )

    # 创建模型
    model = MultimodalGraph2SeqECPE(config.model).to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 创建高性能模型感知解码器
    advanced_decoder = AdvancedModelAwareDecoder(
        model=model,
        token2idx=token2idx,
        idx2token=idx2token,
        emotion_categories=emotion_categories,
        max_length=35
    )

    # 创建高级训练策略
    training_strategy = AdvancedTrainingStrategy(model, advanced_decoder, token2idx)

    # 优化器 - 使用更精细的学习率设置
    optimizer = torch.optim.AdamW([
        {'params': model.plm.parameters(), 'lr': config.training.learning_rate * 0.1, 'weight_decay': 0.01},
        {'params': [p for n, p in model.named_parameters() if 'plm' not in n and 'graph_encoder' in n], 
         'lr': config.training.learning_rate * 0.5, 'weight_decay': 0.001},
        {'params': [p for n, p in model.named_parameters() if 'plm' not in n and 'graph_encoder' not in n], 
         'lr': config.training.learning_rate, 'weight_decay': 0.001}
    ])

    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer, 
        max_lr=[config.training.learning_rate * 0.1, config.training.learning_rate * 0.5, config.training.learning_rate],
        epochs=config.training.epochs,
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )

    best_f1 = 0.0
    best_metrics = {'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
    patience_counter = 0
    max_patience = 10
    target_f1 = 0.6

    logger.info(f"🎯 高性能模型感知训练策略:")
    logger.info(f"  目标F1分数: {target_f1}")
    logger.info(f"  训练阶段: 渐进式 (warmup -> mixed -> advanced)")
    logger.info(f"  解码策略: 束搜索 + 高级约束")
    logger.info(f"  损失函数: 序列级别 + 编辑距离")

    # 训练循环
    for epoch in range(config.training.epochs):
        logger.info(f"\nEpoch {epoch + 1}/{config.training.epochs}")
        
        training_mode = training_strategy.get_training_mode(epoch)
        logger.info(f"训练模式: {training_mode}")

        # 训练阶段
        model.train()
        train_loss = 0.0
        train_batches = 0

        for batch_idx, batch in enumerate(tqdm(train_loader, desc="训练")):
            try:
                target_seqs = prepare_target_sequences(batch, token2idx, max_length=35)
                target_seqs = target_seqs.to(device)

                if target_seqs.sum() == 0:
                    continue

                optimizer.zero_grad()
                
                # 使用高级训练策略
                loss = training_strategy.compute_loss(batch, target_seqs, epoch)

                if torch.isnan(loss) or loss.item() > 20.0:
                    logger.warning(f"异常损失值 {loss.item():.4f}，跳过此批次")
                    continue

                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
                scheduler.step()

                train_loss += loss.item()
                train_batches += 1

            except Exception as e:
                logger.warning(f"训练批次 {batch_idx} 出错: {e}")
                continue

        avg_train_loss = train_loss / max(1, train_batches)
        logger.info(f"平均训练损失: {avg_train_loss:.4f}")

        # 验证阶段
        model.eval()
        all_predictions = []
        all_targets = []
        constraint_satisfied = 0
        total_sequences = 0
        total_matches = 0

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dev_loader, desc="验证")):
                try:
                    target_seqs = prepare_target_sequences(batch, token2idx, max_length=35)
                    target_seqs = target_seqs.to(device)

                    # 使用高性能解码器进行推理
                    predictions = advanced_decoder.advanced_decode(
                        batch, use_constraints=True, beam_size=3, temperature=0.6
                    )

                    # 验证约束满足度和匹配情况
                    for i in range(predictions.size(0)):
                        validation_result = advanced_decoder.validate_sequence_quality(predictions[i])
                        if validation_result['is_valid']:
                            constraint_satisfied += 1
                        total_sequences += 1
                        
                        # 检查匹配情况
                        pred_pairs = set(validation_result['pairs'])
                        if i < target_seqs.size(0):
                            target_tokens = [idx2token.get(idx.item(), '<unk>') for idx in target_seqs[i]]
                            post_processor = UnifiedPostProcessor(mode='optimal')
                            true_pairs = set(post_processor.process(target_tokens))
                            matches = len(pred_pairs & true_pairs)
                            total_matches += matches

                    all_predictions.append(predictions)
                    all_targets.append(target_seqs)

                except Exception as e:
                    logger.warning(f"验证批次 {batch_idx} 出错: {e}")
                    continue

        constraint_satisfaction = constraint_satisfied / max(total_sequences, 1)

        # 计算指标
        if all_predictions and all_targets:
            # 处理不同长度的序列
            max_pred_len = max(pred.size(1) for pred in all_predictions)
            max_target_len = max(target.size(1) for target in all_targets)
            
            # 填充序列
            padded_predictions = []
            for pred in all_predictions:
                if pred.size(1) < max_pred_len:
                    pad_length = max_pred_len - pred.size(1)
                    padded_pred = torch.cat([
                        pred, 
                        torch.zeros(pred.size(0), pad_length, dtype=pred.dtype, device=pred.device)
                    ], dim=1)
                else:
                    padded_pred = pred
                padded_predictions.append(padded_pred)
            
            padded_targets = []
            for target in all_targets:
                if target.size(1) < max_target_len:
                    pad_length = max_target_len - target.size(1)
                    padded_target = torch.cat([
                        target,
                        torch.zeros(target.size(0), pad_length, dtype=target.dtype, device=target.device)
                    ], dim=1)
                else:
                    padded_target = target
                padded_targets.append(padded_target)

            combined_predictions = torch.cat(padded_predictions, dim=0)
            combined_targets = torch.cat(padded_targets, dim=0)

            post_processor = UnifiedPostProcessor(mode='optimal')
            metrics = compute_ecpe_metrics(combined_predictions, combined_targets, post_processor, idx2token)
        else:
            metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}

        logger.info(f"验证指标: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
        logger.info(f"约束满足率: {constraint_satisfaction:.4f}")
        logger.info(f"总匹配对数: {total_matches}")

        # 保存最佳模型
        improved = False
        if metrics['f1'] > best_f1:
            best_f1 = metrics['f1']
            best_metrics = metrics.copy()
            best_metrics['constraint_satisfaction'] = constraint_satisfaction
            best_metrics['total_matches'] = total_matches
            improved = True
            logger.info(f"  ✓ 新的最佳F1: {best_f1:.4f}")

            # 保存模型
            save_path = f"output/best_advanced_model_aware_f1_model_{config.dataset.name}.pt"
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            torch.save(model.state_dict(), save_path)
            logger.info(f"  模型已保存到: {save_path}")

            if best_f1 >= target_f1:
                logger.info(f"🎉 达到目标F1分数 {target_f1}！")

        if improved:
            patience_counter = 0
        else:
            patience_counter += 1
            logger.info(f"  验证指标未改善 ({patience_counter}/{max_patience})")

        # 早停检查
        if patience_counter >= max_patience:
            logger.info(f"早停触发，停止训练")
            break

    # 训练完成总结
    logger.info(f"\n🎯 高性能模型感知训练完成！")
    logger.info(f"最佳F1分数: {best_f1:.4f}")
    logger.info(f"最佳精确率: {best_metrics['precision']:.4f}")
    logger.info(f"最佳召回率: {best_metrics['recall']:.4f}")
    logger.info(f"最佳约束满足率: {best_metrics.get('constraint_satisfaction', 0.0):.4f}")
    logger.info(f"最佳匹配对数: {best_metrics.get('total_matches', 0)}")
    
    logger.info(f"\n🏆 最终结果:")
    logger.info(f"F1分数: {best_f1:.4f}")
    logger.info(f"精确率: {best_metrics['precision']:.4f}")
    logger.info(f"召回率: {best_metrics['recall']:.4f}")
    logger.info(f"约束满足率: {best_metrics.get('constraint_satisfaction', 0.0):.4f}")
    logger.info(f"匹配对数: {best_metrics.get('total_matches', 0)}")


if __name__ == "__main__":
    main()
